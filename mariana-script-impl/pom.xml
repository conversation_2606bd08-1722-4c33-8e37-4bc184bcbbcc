<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mariana-script-family</artifactId>
        <groupId>com.shuyun</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mariana-script-impl</artifactId>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <shuyun.common.version>2023.*********.3-SNAPSHOT</shuyun.common.version>
        <shuyun.tianji.version>1.0.3</shuyun.tianji.version>

        <fastjson.version>1.2.73</fastjson.version>
        <guava.version>27.0-jre</guava.version>
        <hibernate-validator.version>6.0.13.Final</hibernate-validator.version>
        <jjwt.version>0.9.1</jjwt.version>
        <shuyun-platform-starter.version>*******</shuyun-platform-starter.version>
        <hutool.version>5.7.16</hutool.version>
        <shenyu.version>2.6.1</shenyu.version>
        <nacos.version>2.3.0</nacos.version>
        <hutool-all.version>5.8.16</hutool-all.version>

        <cem-web-sdk.version>1.0.6-SNAPSHOT</cem-web-sdk.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>mariana-script-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>cem-std-member-protocol</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.36</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.javaparser</groupId>
            <artifactId>javaparser-core</artifactId>
            <version>3.23.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.72</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
            <version>5.3.20</version>
            <!--<scope>provided</scope>-->
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.12.0</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>common-web</artifactId>
            <version>2023.*********.3-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.7.0</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
package com.shuyun.mariana.script.impl.kylin_product.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MemberPointChangeReqBodyScript extends BaseKylinProScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        JSONObject bodyParam = JSON.parseObject(reqBodyInJson);

        JSONObject jsonObjResult = new JSONObject();

        JSONObject appCfgJson = JSON.parseObject(bodyParam.getString("appCfgJson"));
        if (Objects.nonNull(appCfgJson)) {
            String pointAccountId = appCfgJson.getString("pointAccountId");
            jsonObjResult.put("pointAccountId", pointAccountId);
            jsonObjResult.put("channelType", appCfgJson.getString("channel"));
        }
        jsonObjResult.put("memberId", bodyParam.getString("memberId"));

        //拼接公共参数
        jsonObjResult.put("desc", bodyParam.getString("desc"));

        jsonObjResult.put("point", bodyParam.getBigDecimal("changePoint"));

        JSONObject bizExtObj = JSON.parseObject(bodyParam.getString("bizExtJson"));

        //    POINT_GAIN("积分发放"),
        //    POINT_CONSUME("积分消费"),
        //    POINT_REVERT("返还消费积分"),
        //    POINT_FREEZE("冻结积分"),
        //    POINT_UNFREEZE("解冻积分"),
        //    POINT_FREEZE_CONSUME("冻结消费");

        if (StringUtils.isNotEmpty(bodyParam.getString("pointChangeType"))){
            String pointChangeType = bodyParam.getString("pointChangeType");
            //积分发放
            if ("POINT_GAIN".equalsIgnoreCase(pointChangeType)){
                String created = bodyParam.getString("created");
                String expired = bodyParam.getString("expired");
                if (created!=null&&!"".equalsIgnoreCase(created)){
                    jsonObjResult.put("effectDate", created);
                }
                if (expired!=null&&!"".equalsIgnoreCase(expired)){
                    LocalDateTime expireTime = LocalDateTime.parse(expired, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//                    DateTimeFormatter stanardZonedDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
                    DateTimeFormatter stanardZonedDateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+08:00");
                    String formatExpire = expireTime.format(stanardZonedDateTimeFormatter);
                    jsonObjResult.put("overdueDate", formatExpire);
                }
                jsonObjResult.put("recordType", "SEND");
                jsonObjResult.put("triggerId", bodyParam.getString("sequence"));
            }
            //积分消费
            if ("POINT_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObjResult.put("recordType", "DEDUCT");
                jsonObjResult.put("triggerId", bodyParam.getString("sequence"));
                if (Objects.nonNull(bizExtObj)) {
                    String shortTermPoint = bizExtObj.getString("shortTermPoint");
                    if (StringUtils.isNotEmpty(shortTermPoint)) {
                        jsonObjResult.put("point", shortTermPoint);
                        jsonObjResult.put("KZZD3", "特殊兑换");
                    }
                }
            }
            //解冻积分
            if ("POINT_UNFREEZE".equalsIgnoreCase(pointChangeType)){
                jsonObjResult.put("businessId", bodyParam.getString("freezeId"));
            }
            //冻结积分
            if ("POINT_FREEZE".equalsIgnoreCase(pointChangeType)){
                jsonObjResult.put("businessId", bodyParam.getString("sequence"));
            }
            //冻结消费
            if ("POINT_FREEZE_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObjResult.put("businessId", bodyParam.getString("freezeId"));
            }
            //返还消费积分
            if ("POINT_REVERT".equalsIgnoreCase(pointChangeType)){
                jsonObjResult.put("recordType","DEDUCT");
                jsonObjResult.put("triggerId", bodyParam.getString("sequence"));
                jsonObjResult.put("tradeId", bodyParam.getString("freezeId"));

                if (Objects.nonNull(bizExtObj)) {
                    String shortTermPoint = bizExtObj.getString("shortTermPoint");
                    if (StringUtils.isNotEmpty(shortTermPoint)) {
                        jsonObjResult.put("KZZD3", "特殊兑换");
                    }
                }

            }
        }
        String result = jsonObjResult.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, "{\"appCfgJson\":\"{\\\"cardPlanId\\\":\\\"60001\\\",\\\"gradePlanId\\\":\\\"60005\\\",\\\"programCode\\\":\\\"whoo\\\",\\\"channel\\\":\\\"WECHAT\\\",\\\"pointAccountId\\\":\\\"60037\\\"}\",\"appId\":\"wxdd0a621948ccc675\",\"appKey\":\"**********\",\"changePoint\":10,\"desc\":\"每日签到奖励\",\"fetchByPage\":false,\"memberId\":\"WHOO9YZIQR\",\"middlePlat\":\"kylin\",\"openId\":\"oP9385cVbkWeTs01tBc6AzhlDBfo\",\"page\":1,\"pageSize\":50,\"platCode\":\"WEIXIN\",\"pointChangeType\":\"POINT_GAIN\",\"sequence\":\"81edcb17d582467085248b60ab082997\",\"source\":\"INTERACT\"}");
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {

        MemberPointChangeReqBodyScript script = new MemberPointChangeReqBodyScript();
//        script.run("{\"appCfgJson\":\"{\\\"cardPlanId\\\":\\\"60001\\\",\\\"gradePlanId\\\":\\\"60005\\\",\\\"programCode\\\":\\\"whoo\\\",\\\"channel\\\":\\\"WECHAT\\\",\\\"pointAccountId\\\":\\\"60037\\\"}\",\"appId\":\"wxdd0a621948ccc675\",\"appKey\":\"**********\",\"changePoint\":10,\"desc\":\"每日签到奖励\",\"fetchByPage\":false,\"memberId\":\"WHOO9YZIQR\",\"middlePlat\":\"kylin\",\"openId\":\"oP9385cVbkWeTs01tBc6AzhlDBfo\",\"page\":1,\"pageSize\":50,\"platCode\":\"WEIXIN\",\"pointChangeType\":\"POINT_GAIN\",\"sequence\":\"81edcb17d582467085248b60ab082997\",\"source\":\"INTERACT\"}");
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
package com.shuyun.mariana.script.impl.xhsd.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseXhsdScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        GradeMetaQueryReq gradeMetaQueryReq = JSON.parseObject(reqBodyInJson, GradeMetaQueryReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(gradeMetaQueryReq);

        JSONObject extObj = JSON.parseObject(gradeMetaQueryReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String gradeBizType = extObj.getString("gradeBizType");
            if (gradeBizType!=null&&!"".equalsIgnoreCase(gradeBizType)){
                jsonObject.put("gradeBizType",gradeBizType);
                if ("MEDAL".equalsIgnoreCase(gradeBizType)){
                    jsonObject.put("rulesRequired",false);
                }
            }
        }
        String gradeBizType = jsonObject.getString("gradeBizType");
        if (gradeBizType==null || "".equalsIgnoreCase(gradeBizType)){
            jsonObject.put("gradeBizType","GRADE");
            jsonObject.put("rulesRequired",true);
        }
        if (extObj!=null){
            Boolean rulesRequired = extObj.getBoolean("rulesRequired");
            if (rulesRequired!=null){
                jsonObject.put("rulesRequired",rulesRequired);
            }
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".gradeMeta.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @IgnoreGen
    public static void main(String[] args) {
        MdyReqBodyScript mdyReqBodyScript = new MdyReqBodyScript();
        mdyReqBodyScript.genReqBodyScript(true);
    }
}

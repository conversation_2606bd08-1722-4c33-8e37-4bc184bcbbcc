package com.shuyun.mariana.script.impl.dvf.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberPointQueryReq memberPointQueryReq = JSON.parseObject(reqBodyIn<PERSON><PERSON>, MemberPointQueryReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberPointQueryReq);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.memPoint.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.asics.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员详情查询请求体转换脚本
 * 功能：将内部标准MemberDetailReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        
        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();
        
        //解析配置信息
        String appCfgJson = memberDetailReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        
        String bizExtJson = memberDetailReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 映射到ASICS字段格式 - 支持多种渠道查询

        // 获取查询类型
        String queryType = null;
        if (bizExtObj != null) {
            queryType = bizExtObj.getString("queryType");
        }

        // 会员ID查询
        String memberId = inputParam.getString("memberId");

        if (StringUtils.isNotEmpty(memberId)) {
            // 通过memberId查询，映射为memberCode
            asicsObject.put("memberCode", memberId);
        } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile")) {
            // 通过手机号查询
            asicsObject.put("phone", memberDetailReq.getMobile());
        } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("unionId")) {
            // 通过unionId查询
            asicsObject.put("unionId", memberDetailReq.getUnionId());
        } else {
            asicsObject.put("wxShopOpenId", memberDetailReq.getOpenId());
        }

        // 扩展查询标志
        if (inputParam.containsKey("extendQueryFlag")) {
            asicsObject.put("extendQueryFlag", inputParam.getBoolean("extendQueryFlag"));
        } else if (bizExtObj != null && bizExtObj.containsKey("extendQueryFlag")) {
            asicsObject.put("extendQueryFlag", bizExtObj.getBoolean("extendQueryFlag"));
        } else {
            asicsObject.put("extendQueryFlag", false); // 默认不扩展查询
        }
        
        // 设置渠道信息和会员系统ID - 使用基类方法
        setChannel(asicsObject, appCfgObj, inputParam);
        setMembershipSystemId(asicsObject, appCfgObj, inputParam);

        String result = asicsObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memDetail.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("memberCode", "ASICS_CODE_001");
        object.put("phone", "18710426216");
        object.put("wxShopOpenId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("yzOpenId", "yz_open_id_001");
        object.put("extendQueryFlag", true);
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"memberCode\":\"ASICS_EXT_001\",\"extendQueryFlag\":true,\"queryType\":\"memberId\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

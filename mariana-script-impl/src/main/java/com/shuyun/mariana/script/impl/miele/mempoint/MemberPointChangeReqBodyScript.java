package com.shuyun.mariana.script.impl.miele.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MemberPointChangeReqBodyScript extends BaseMieleScriptBase {
    @Override
    public String run(String reqBodyInJson) {
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        //拼接公共参数
        jsonObject.put("programCode", "Miele");
        jsonObject.put("channelType", "WECHAT");
        jsonObject.put("businessId", jsonObject.getString("sequence"));
        jsonObject.put("desc", jsonObject.getString("desc"));
        String bizExtJson=jsonObject.getString("bizExtJson");
        JSONObject extObj = JSON.parseObject(bizExtJson);
        String memberId=jsonObject.getString("memberId");
        if (memberId==null || "".equalsIgnoreCase(memberId)){
            if (Objects.nonNull(extObj)) {
                memberId = extObj.getString("memberId");
            }
        }
        jsonObject.put("memberId", memberId);
        JSONObject appCfgJson = JSON.parseObject(jsonObject.getString("appCfgJson"));
        if (Objects.nonNull(appCfgJson)) {
            String pointAccountId = appCfgJson.getString("pointAccountId");
            if (pointAccountId!=null&&!"".equalsIgnoreCase(pointAccountId)){
                jsonObject.put("pointAccountId", Long.parseLong(pointAccountId));
            }
        }
        jsonObject.put("point", jsonObject.getBigDecimal("changePoint"));
        if (jsonObject.get("pointChangeType")!=null&& !"".equals(jsonObject.getString("pointChangeType"))){
            String pointChangeType = jsonObject.getString("pointChangeType");
            //积分发放
            if ("POINT_GAIN".equalsIgnoreCase(pointChangeType)){
                String created = jsonObject.getString("created");
                String expired = jsonObject.getString("expired");
                if (created!=null&&!"".equalsIgnoreCase(created)){
                    jsonObject.put("effectiveDate", created);
                }
                if (expired!=null&&!"".equalsIgnoreCase(expired)){
                    jsonObject.put("overdueDate", expired);
                }
               // jsonObject.put("changeMode", "SEND");
            }
            //积分消费
            if ("POINT_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("changeMode", "DEDUCT");
            }
            //解冻积分
            if ("POINT_UNFREEZE".equalsIgnoreCase(pointChangeType)){

            }
            //冻结积分
            if ("POINT_FREEZE".equalsIgnoreCase(pointChangeType)){

            }
            //冻结消费
            if ("POINT_FREEZE_CONSUME".equalsIgnoreCase(pointChangeType)){

            }
            //返还消费积分
            if ("POINT_REVERT".equalsIgnoreCase(pointChangeType)){
                String created = jsonObject.getString("created");
                String expired = jsonObject.getString("expired");
                if (created!=null&&!"".equalsIgnoreCase(created)){
                    jsonObject.put("effectiveDate", created);
                }
                if (expired!=null&&!"".equalsIgnoreCase(expired)){
                    jsonObject.put("overdueDate", expired);
                }
            }
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("sequence", "sequence");
        object.put("expired", "expired");
        object.put("created", "created");
        object.put("openId", "id");
        object.put("source", "source");
        object.put("changePoint", 90);
        object.put("operator", "operator");
        object.put("desc", "desc");
        object.put("pointChangeType", "POINT_CONSUME");
        object.put("bizExtJson", "{\"memberId\":\"ec57ac9dfa8f418f98bf07f6ff3f0fae\"}");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
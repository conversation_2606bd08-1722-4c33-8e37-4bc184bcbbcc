package com.shuyun.mariana.script.impl.dvf.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberRegReq);

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        jsonObject.put("registerTime", formatter.format(new Date()));

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberRegReq);
        JSONObject extObj = JSON.parseObject(memberRegReq.getBizExtJson());
        String gender=jsonObject.getString("gender");
        if (gender==null || "".equalsIgnoreCase(gender)){
            //反序列化扩展字段
            if (Objects.nonNull(extObj)) {
                String genderStr = extObj.getString("gender");
                jsonObject.put("gender", genderStr);
            }
        }

        String appCfgJson = memberRegReq.getAppCfgJson();
        if (appCfgJson!=null&&!"".equalsIgnoreCase(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            jsonObject.put("registerShopCode", appCfgObj.getString("registerShopCode"));
            jsonObject.put("registerShopName", appCfgObj.getString("registerShopName"));
        } else {
            jsonObject.put("registerShopCode","DVFCLUB");
            jsonObject.put("registerShopName","DVFEZR开卡虚拟店02");
        }

        String defaultRegisterShopCode = jsonObject.getString("registerShopCode");
        if(defaultRegisterShopCode==null || "".equalsIgnoreCase(defaultRegisterShopCode)){
            jsonObject.put("registerShopCode","DVFCLUB");
            jsonObject.put("registerShopName","DVFEZR开卡虚拟店02");
        }
        JSONObject extras=new JSONObject();
        if (Objects.nonNull(extObj)) {
            String registerGuideStr = extObj.getString("registerGuide");
            String registerGuideNameStr = extObj.getString("registerGuideName");
            String registerShopCodeStr = extObj.getString("registerShopCode");
            String registerShopNameStr = extObj.getString("registerShopName");
            if (registerGuideStr!=null && !"".equalsIgnoreCase(registerGuideStr)){
                jsonObject.put("registerGuide", registerGuideStr);
                jsonObject.put("ascriptionChannel", "WECHAT");
                jsonObject.put("ascriptionChannelName", "微信");
            }
            if (registerGuideNameStr!=null && !"".equalsIgnoreCase(registerGuideNameStr)){
                extras.put("enrollGuideName",registerGuideNameStr);
            }
            if (registerShopCodeStr!=null && !"".equalsIgnoreCase(registerShopCodeStr)){
                jsonObject.put("registerShopCode",registerShopCodeStr);
                jsonObject.put("ascriptionShopCode",registerShopCodeStr);
            }
            if (registerShopNameStr!=null && !"".equalsIgnoreCase(registerShopNameStr)){
                jsonObject.put("registerShopName",registerShopNameStr);
                jsonObject.put("ascriptionShopName",registerShopNameStr);
            }
        }
        jsonObject.put("extras",extras);
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("bizExtJson","{\"gender\":\"M\",\"registerGuide\":\"A99999\",\"registerGuideName\":\"数云业务部-丽丽\",\"registerShopCode\":\"SHOP002\",\"registerShopName\":\"SHOP002\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.meilv.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

public class MdyRspBodyScript extends BaseMeiLvScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        String error_code = rspObj.getString("error_code");
        if (error_code!=null&&!"".equalsIgnoreCase(error_code)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("error_code"));
            errRestWrap.setMessage(rspObj.getString("msg"));
            return JSON.toJSONString(errRestWrap);
        }
        JSONArray dataObj = rspObj.getJSONArray("data");
        List<ShopDto> resultJsonArray = new ArrayList<>();
        PageWrap pageWrap = new PageWrap();
        pageWrap.setPage(1);
        pageWrap.setPageSize(10);
        pageWrap.setTotalCount(10);
        if (Objects.nonNull(dataObj)) {
            for (int i = 0; i < dataObj.size(); i++) {
                ShopDto shopDto = new ShopDto();
                JSONObject jsonObj = dataObj.getJSONObject(i);
                shopDto.setShopCode(jsonObj.getString("shopCode"));
                shopDto.setShopName(jsonObj.getString("shopName"));
                shopDto.setShopType(jsonObj.getString("shopTypeCode"));
                String shopStatus = jsonObj.getString("status");
                if ("OPENING".equalsIgnoreCase(shopStatus)){
                    shopDto.setStatus("open");
                }else {
                    shopDto.setStatus("closed");
                }
                shopDto.setCityName(jsonObj.getString("cityName"));
                shopDto.setAddress(jsonObj.getString("address"));
                shopDto.setLongitude(jsonObj.getString("longitude"));
                shopDto.setLatitude(jsonObj.getString("latitude"));
                shopDto.setContactTel(jsonObj.getString("contactTel"));
                resultJsonArray.add(shopDto);
            }
        }
        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".shop.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("shopCode", "10001");
        dataObj.put("shopName", "测试");
        JSONArray array=new JSONArray();
        array.add(dataObj);
        object.put("data", array);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.asics.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分流水查询响应体转换脚本
 * 功能：将ASICS系统的响应转换为内部标准PointRecordDto协议
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        List<PointRecordDto> pointRecordDtos = new ArrayList<>();
        PageWrap<PointRecordDto> pageWrap = new PageWrap<>();
        
        // 处理ASICS系统的响应数据
        JSONArray contentArray = rspObj.getJSONArray("content");
        if (Objects.nonNull(contentArray)) {
            for (int i = 0; i < contentArray.size(); i++) {
                JSONObject recordObj = contentArray.getJSONObject(i);
                PointRecordDto pointRecordDto = new PointRecordDto();
                
                // 映射ASICS字段到标准字段
                // 积分数量
                if (recordObj.containsKey("points")) {
                    Double points = recordObj.getDouble("points");
                    pointRecordDto.setSignedPoint(points);
                    // 同时设置 point 字段（绝对值）
                    pointRecordDto.setPoint(Math.abs(points));
                }
                
                // 创建时间
                if (recordObj.containsKey("createTime")) {
                    pointRecordDto.setChangeTime(recordObj.getString("createTime"));
                }
                
                // 来源类型映射到变更类型
                String sourceType = recordObj.getString("sourceType");
                String recordType = mapSourceTypeToChangeType(sourceType);
                pointRecordDto.setRecordType(recordType);
                
                // 描述信息
                if (recordObj.containsKey("summary")) {
                    pointRecordDto.setDescription(recordObj.getString("summary"));
                }
                
                // 渠道信息
                if (recordObj.containsKey("channel")) {
                    Object channelObj = recordObj.get("channel");
                    if (channelObj instanceof Integer) {
                        pointRecordDto.setChannelType(mapChannelCodeToName((Integer) channelObj));
                    } else if (channelObj instanceof String) {
                        pointRecordDto.setChannelType((String) channelObj);
                    }
                }
                
                // 设置其他默认字段
                
                // 保存原始数据作为扩展信息
                pointRecordDto.setProjectExt(JSON.toJSONString(recordObj));
                
                pointRecordDtos.add(pointRecordDto);
            }
        }
        
        // 设置分页信息
        pageWrap.setItems(pointRecordDtos);
        
        // 处理分页元数据
        if (rspObj.containsKey("number")) {
            pageWrap.setPage(rspObj.getInteger("number"));
        }
        if (rspObj.containsKey("size")) {
            pageWrap.setPageSize(rspObj.getInteger("size"));
        }
        if (rspObj.containsKey("totalElements")) {
            pageWrap.setTotalCount(rspObj.getInteger("totalElements"));
        }
        // totalPages 不需要设置，PageWrap 会自动计算

        // 构建标准响应格式
        RestWrap<PageWrap<PointRecordDto>> pointRecordRspRestWrap = new RestWrap<>();
        pointRecordRspRestWrap.buildSuccess(pageWrap);

        String result = JSON.toJSONString(pointRecordRspRestWrap);
        return result;
    }
    
    /**
     * 将ASICS的sourceType映射到标准的changeType
     */
    private String mapSourceTypeToChangeType(String sourceType) {
        if (sourceType == null) {
            return "未知";
        }

        // 先尝试按数字解析
        try {
            int sourceTypeCode = Integer.parseInt(sourceType);
            return mapSourceTypeCodeToText(sourceTypeCode);
        } catch (NumberFormatException e) {
            // 如果不是数字，按字符串处理
            return mapSourceTypeStringToText(sourceType);
        }
    }

    /**
     * 将sourceType数字代码映射为中文描述
     */
    private String mapSourceTypeCodeToText(int sourceTypeCode) {
        switch (sourceTypeCode) {
            case 1:
                return "订单";
            case 2:
                return "退单";
            case 3:
                return "活动";
            case 4:
                return "手动";
            case 5:
                return "兑礼消耗";
            case 6:
                return "兑礼取消";
            case 7:
                return "过期";
            case 8:
                return "调整积分";
            case 9:
                return "签到";
            default:
                return "其他(" + sourceTypeCode + ")";
        }
    }

    /**
     * 将sourceType字符串映射为中文描述（兼容旧格式）
     */
    private String mapSourceTypeStringToText(String sourceType) {
        switch (sourceType.toUpperCase()) {
            case "EARN":
            case "ORDER":
                return "订单";
            case "REFUND":
            case "RETURN":
                return "退单";
            case "ACTIVITY":
            case "BONUS":
                return "活动";
            case "MANUAL":
                return "手动";
            case "REDEEM":
            case "CONSUME":
            case "USE":
                return "兑礼消耗";
            case "CANCEL":
                return "兑礼取消";
            case "EXPIRE":
            case "EXPIRED":
                return "过期";
            case "ADJUST":
                return "调整积分";
            case "SIGNIN":
            case "CHECKIN":
                return "签到";
            default:
                return sourceType;
        }
    }
    
    /**
     * 将渠道代码映射到中文渠道名称
     */
    private String mapChannelCodeToName(Integer channelCode) {
        if (channelCode == null) {
            return "未知渠道";
        }

        switch (channelCode) {
            case 1:
                return "天猫AT";
            case 2:
                return "天猫AS";
            case 3:
                return "天猫专卖店";
            case 4:
                return "微信会员中心";
            case 5:
                return "品牌小程序";
            case 15:
                return "微信会员中心"; // 兼容原有的微信渠道代码
            default:
                return "其他渠道(" + channelCode + ")";
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".pointRecord.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "0");
        object.put("message", "success");
        
        // 构建积分流水数据
        JSONArray contentArray = new JSONArray();

        JSONObject record1 = new JSONObject();
        record1.put("channel", 4);
        record1.put("createTime", "2024-09-17 16:28:38");
        record1.put("points", 100);
        record1.put("sourceType", "1"); // 1-订单
        record1.put("summary", "购物获得积分");
        contentArray.add(record1);

        JSONObject record2 = new JSONObject();
        record2.put("channel", 1);
        record2.put("createTime", "2024-09-16 14:20:15");
        record2.put("points", -50);
        record2.put("sourceType", "5"); // 5-兑礼消耗
        record2.put("summary", "积分兑换商品");
        contentArray.add(record2);

        JSONObject record3 = new JSONObject();
        record3.put("channel", 5);
        record3.put("createTime", "2024-09-15 10:30:20");
        record3.put("points", 200);
        record3.put("sourceType", "9"); // 9-签到
        record3.put("summary", "签到奖励积分");
        contentArray.add(record3);

        JSONObject record4 = new JSONObject();
        record4.put("channel", 4);
        record4.put("createTime", "2024-09-14 12:15:30");
        record4.put("points", 50);
        record4.put("sourceType", "3"); // 3-活动
        record4.put("summary", "活动奖励积分");
        contentArray.add(record4);
        
        object.put("content", contentArray);
        object.put("number", 0);
        object.put("size", 20);
        object.put("totalElements", 4);
        object.put("totalPages", 1);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

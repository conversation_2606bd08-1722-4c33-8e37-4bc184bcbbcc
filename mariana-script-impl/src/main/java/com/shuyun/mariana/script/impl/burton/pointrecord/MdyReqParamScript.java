package com.shuyun.mariana.script.impl.burton.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.pointrecord.MemPointRecordQueryReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemPointRecordQueryReq pointRecordQueryReq = JSON.parseObject(reqBodyInJson, MemPointRecordQueryReq.class);
        JSONObject parseObject = JSON.parseObject(reqBodyInJson);
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);
        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        addParameters.put("customerNo", pointRecordQueryReq.getAppId() + "_" + pointRecordQueryReq.getOpenId());
        addParameters.put("unionId", pointRecordQueryReq.getUnionId());
        addParameters.put("endTime", pointRecordQueryReq.getEndTime());
        addParameters.put("mobile", pointRecordQueryReq.getMobile());
        addParameters.put("memberId", parseObject.getString("memberId"));
        addParameters.put("page", pointRecordQueryReq.getPage().toString());
        addParameters.put("pageSize", pointRecordQueryReq.getPageSize().toString());
        addParameters.put("startTime", pointRecordQueryReq.getStartTime());
        JSONObject jsonObject = JSON.parseObject(pointRecordQueryReq.getBizExtJson());
        if (Objects.nonNull(jsonObject)) {
            addParameters.put("recordType",jsonObject.getString("recordType") );
        }
        JSONObject appCfgJson = JSON.parseObject(pointRecordQueryReq.getAppCfgJson());
        if (Objects.nonNull(appCfgJson)) {
            addParameters.put("memberType", appCfgJson.getString("memberType"));
        }
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);
        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".pointRecord.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.tagmetaquery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegRsp;
import com.shuyun.cem.std.member.protocol.memtag.tagmeta.kylin.TagMetaDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        ArrayList<TagMetaDto> tagMetaDtos = new ArrayList<>();
        JSONArray content = rspObj.getJSONArray("content");
        for (int i = 0; i < content.size(); i++) {
            TagMetaDto tagMetaDto = new TagMetaDto();
            JSONObject jsonObject = content.getJSONObject(i);
            tagMetaDto.setId(jsonObject.getString("id"));
            tagMetaDto.setName(jsonObject.getString("name"));
            tagMetaDtos.add(tagMetaDto);
        }
        RestWrap<List<TagMetaDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(tagMetaDtos);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".tagMetaQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"totalElement\": 286,\n" +
                "    \"pageNo\": 1,\n" +
                "    \"pageSize\": 10,\n" +
                "    \"content\": [\n" +
                "        {\n" +
                "            \"id\": \"20240829172316372141847030231542\",\n" +
                "            \"name\": \"MM wave1 老会老客 低低高C\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29213,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 204,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:23:16+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:23:16.389+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低低高C\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829172309107141847030231541\",\n" +
                "            \"name\": \"MM wave1 老会老客 低低高B\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29212,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 410,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:23:09+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:23:09.119+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低低高B\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829172302795141847030231540\",\n" +
                "            \"name\": \"MM wave1 老会老客 低低高A\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29211,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 410,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:23:03+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:23:02.807+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低低高A\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829170741499141847030231539\",\n" +
                "            \"name\": \"MM wave1 老会老客 低高高C\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29210,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 701,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:07:42+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:07:41.511+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低高高C\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829170735221141847030231538\",\n" +
                "            \"name\": \"MM wave1 老会老客 低高高B\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29209,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 1404,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:07:35+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:07:35.233+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低高高B\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829170729257141847030231537\",\n" +
                "            \"name\": \"MM wave1 老会老客 低高高A\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29208,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 1404,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T17:07:29+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T17:07:29.269+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低高高A\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829164011991141847030231534\",\n" +
                "            \"name\": \"MM wave1 老会老客 高低低C\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29207,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 1892,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T16:40:12+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T16:40:12.002+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 高低低C\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829164002373141847030231533\",\n" +
                "            \"name\": \"MM wave1 老会老客 高低低B\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29206,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 3787,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T16:40:02+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T16:40:02.385+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 高低低B\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829163945901141847030231532\",\n" +
                "            \"name\": \"MM wave1 老会老客 高低低A\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29205,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 3786,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T16:39:46+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T16:39:45.912+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 高低低A\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"20240829163422139141847030231531\",\n" +
                "            \"name\": \"MM wave1 老会老客 低高低C\",\n" +
                "            \"origin\": \"FRONT\",\n" +
                "            \"type\": \"MANUAL\",\n" +
                "            \"uniqueSequence\": 29204,\n" +
                "            \"categoryId\": 46,\n" +
                "            \"categoryPath\": \"/46\",\n" +
                "            \"viewId\": 1,\n" +
                "            \"customerCount\": 2184,\n" +
                "            \"updatePolicy\": \"MANUAL_TAG\",\n" +
                "            \"period\": \"\",\n" +
                "            \"status\": \"CALCULATE_SUCCESSFUL\",\n" +
                "            \"errorMessage\": \"\",\n" +
                "            \"nextCalcTime\": \"5000-01-01T00:00:00+08:00\",\n" +
                "            \"createTime\": \"2024-08-29T16:34:22+08:00\",\n" +
                "            \"createBy\": \"mahong\",\n" +
                "            \"updateTime\": \"2024-08-29T16:34:22.151+08:00\",\n" +
                "            \"updateBy\": \"mahong\",\n" +
                "            \"tagValueType\": \"NO_VALUE\",\n" +
                "            \"isFailedRetry\": true,\n" +
                "            \"version\": \"1.0.0\",\n" +
                "            \"_i18nPayload\": {\n" +
                "                \"name\": {\n" +
                "                    \"zh-CN\": \"MM wave1 老会老客 低高低C\"\n" +
                "                },\n" +
                "                \"description\": {}\n" +
                "            },\n" +
                "            \"i18nConfig\": {}\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.dvf.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

public class MdyRspBodyScript extends BaseDvfScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        Boolean success = rspObj.getBoolean("success");
        if (!success) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }
        JSONObject data = rspObj.getJSONObject("data");

        List<ShopDto> resultJsonArray = new ArrayList<>();
        PageWrap pageWrap = new PageWrap();
        pageWrap.setPage(data.getInteger("page"));
        pageWrap.setPageSize(data.getInteger("pageSize"));
        pageWrap.setTotalCount(data.getInteger("totalCount"));
        JSONArray dataObj = data.getJSONArray("items");
        if (Objects.nonNull(dataObj)) {
            for (int i = 0; i < dataObj.size(); i++) {
                ShopDto shopDto = new ShopDto();
                JSONObject jsonObj = dataObj.getJSONObject(i);
                shopDto.setShopCode(jsonObj.getString("shopCode"));
                shopDto.setShopName(jsonObj.getString("shopName"));
                shopDto.setStatus(jsonObj.getString("shopStatus"));
                shopDto.setCityName(jsonObj.getString("cityName"));
                shopDto.setAddress(jsonObj.getString("address"));
                shopDto.setLatitude(jsonObj.getString("latitude"));
                shopDto.setLongitude(jsonObj.getString("longitude"));
                shopDto.setContactTel(jsonObj.getString("contactTel"));
                shopDto.setOpenTime(jsonObj.getString("openDate"));
                shopDto.setCloseTime(jsonObj.getString("closeDate"));
                shopDto.setShopType(jsonObj.getString("shopType"));
                String openTime = jsonObj.getString("openTime");
                String closeTime = jsonObj.getString("closeTime");
                if (openTime!=null && closeTime!=null){
                    JSONObject extJson=new JSONObject();
                    extJson.put("openTime",openTime);
                    extJson.put("closeTime",closeTime);
                    shopDto.setProjectExt(extJson.toJSONString());
                }
                resultJsonArray.add(shopDto);
            }
        }
        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".shop.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("page", "1");
        dataObj.put("pageSize", "10");
        dataObj.put("totalCount", "10");
        JSONArray array=new JSONArray();


        JSONObject dataObjItem = new JSONObject();
        dataObjItem.put("shopCode", "1");
        dataObjItem.put("shopName", "10");
        array.add(dataObjItem);
        dataObj.put("items", array);
        object.put("data", dataObj);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

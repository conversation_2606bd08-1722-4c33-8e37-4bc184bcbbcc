package com.shuyun.mariana.script.impl.ccmsv2.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.ccmsv2.BaseCcmsV2ScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsV2ScriptBase {
    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject data = rspObj.getJSONObject("data");
        ArrayList<Map> maps = new ArrayList<>();
        if (Objects.nonNull(data)){
           JSONArray grades = data.getJSONArray("grades");
           for (int i = 0; i < grades.size(); i++) {
               HashMap<String, Object> map = new HashMap<>();
               map.put("gradeId",grades.getJSONObject(i).getString("gradeId"));
               map.put("gradeName",grades.getJSONObject(i).getString("gradeName"));
               map.put("gradeSort",grades.getJSONObject(i).getString("gradeSort"));
               map.put("projectExt",grades.getJSONObject(i).toJSONString());
               maps.add(map);
           }
       }
        RestWrap<List<Map>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(maps);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "grademeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "        \"grades\": [\n" +
                "            {\n" +
                "                \"gradeId\": 100026043,\n" +
                "                \"gradeSort\": 1,\n" +
                "                \"gradeName\": \"普卡会员\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"gradeId\": 100026044,\n" +
                "                \"gradeSort\": 2,\n" +
                "                \"gradeName\": \"黑卡会员\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"gradeId\": 100026045,\n" +
                "                \"gradeSort\": 3,\n" +
                "                \"gradeName\": \"银卡会员\"\n" +
                "            },\n" +
                "            {\n" +
                "                \"gradeId\": 100026046,\n" +
                "                \"gradeSort\": 4,\n" +
                "                \"gradeName\": \"金卡会员\"\n" +
                "            }\n" +
                "        ]\n" +
                "    }");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

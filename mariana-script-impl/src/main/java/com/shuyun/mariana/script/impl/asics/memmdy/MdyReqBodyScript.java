package com.shuyun.mariana.script.impl.asics.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员修改请求体转换脚本
 * 功能：将内部标准MemberMdyReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        
        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();
        
        //解析配置信息
        String appCfgJson = memberMdyReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        
        String bizExtJson = memberMdyReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 映射到ASICS字段格式 - 支持所有可修改字段
        
        // 基础信息字段
        if (StringUtils.isNotEmpty(memberMdyReq.getUnionId())) {
            asicsObject.put("unionId", memberMdyReq.getUnionId());
        }
        
        if (StringUtils.isNotEmpty(memberMdyReq.getMobile())) {
            asicsObject.put("phone", memberMdyReq.getMobile());
        }
        
        if (StringUtils.isNotEmpty(memberMdyReq.getMemberName())) {
            asicsObject.put("name", memberMdyReq.getMemberName());
        }
        asicsObject.put("memberCode", inputParam.getString("memberId"));
        
        // 性别转换
        String gender = memberMdyReq.getGender();
        if (bizExtObj != null && StringUtils.isNotEmpty(bizExtObj.getString("gender"))) {
            gender = bizExtObj.getString("gender");
        }
        if (StringUtils.isNotEmpty(gender)) {
            String sexValue = "未知";
            if ("M".equals(gender) || "男".equals(gender)) {
                sexValue = "男";
            } else if ("F".equals(gender) || "女".equals(gender)) {
                sexValue = "女";
            }
            asicsObject.put("sex", sexValue);
        }
        
        // 从输入参数或扩展字段中获取ASICS特有字段
        if (bizExtObj != null) {
            // 地址相关
            putIfNotEmpty(asicsObject, "address", bizExtObj.getString("address"));
            putIfNotEmpty(asicsObject, "province", bizExtObj.getString("province"));
            putIfNotEmpty(asicsObject, "city", bizExtObj.getString("city"));
            putIfNotEmpty(asicsObject, "county", bizExtObj.getString("county"));
            
            // 个人信息
            putIfNotEmpty(asicsObject, "email", bizExtObj.getString("email"));
            putIfNotEmpty(asicsObject, "height", bizExtObj.getString("height"));
            putIfNotEmpty(asicsObject, "weight", bizExtObj.getString("weight"));
            putIfNotEmpty(asicsObject, "shoeSize", bizExtObj.getString("shoeSize"));
            
            // 运动相关
            putIfNotEmpty(asicsObject, "runningTag", bizExtObj.getString("runningTag"));
            putIfNotEmpty(asicsObject, "weekDistance", bizExtObj.getString("weekDistance"));
            putIfNotEmpty(asicsObject, "spotPreference", bizExtObj.getString("spotPreference"));
            putIfNotEmpty(asicsObject, "testResult", bizExtObj.getString("testResult"));
            
            // 装备相关
            putIfNotEmpty(asicsObject, "ownGears", bizExtObj.getString("ownGears"));
            putIfNotEmpty(asicsObject, "extraGears", bizExtObj.getString("extraGears"));
            
            // 能力相关
            putIfNotEmpty(asicsObject, "powers", bizExtObj.getString("powers"));
            putIfNotEmpty(asicsObject, "totalPowers", bizExtObj.getString("totalPowers"));
            putIfNotEmpty(asicsObject, "growths", bizExtObj.getString("growths"));
            
            // 标记相关
            putIfNotEmpty(asicsObject, "archType", bizExtObj.getString("archType"));
            putIfNotEmpty(asicsObject, "asMark", bizExtObj.getString("asMark"));
            putIfNotEmpty(asicsObject, "atMark", bizExtObj.getString("atMark"));
            putIfNotEmpty(asicsObject, "tmark", bizExtObj.getString("tmark"));
            
            // 其他字段
            putIfNotEmpty(asicsObject, "memberCode", bizExtObj.getString("memberCode"));
            putIfNotEmpty(asicsObject, "memberQr", bizExtObj.getString("memberQr"));
            putIfNotEmpty(asicsObject, "bopenId", bizExtObj.getString("bopenId"));
            putIfNotEmpty(asicsObject, "cOpenID", bizExtObj.getString("cOpenID"));
            putIfNotEmpty(asicsObject, "wxUserId", bizExtObj.getString("wxUserId"));
            
            // 数值字段
            if (bizExtObj.containsKey("caller") && bizExtObj.get("caller") != null) {
                asicsObject.put("caller", bizExtObj.getInteger("caller"));
            }
            // membershipSystemId 在后面统一处理
        }
        
        // 从输入参数中直接获取字段（优先级更高）
        putIfNotEmpty(asicsObject, "address", inputParam.getString("address"));
        putIfNotEmpty(asicsObject, "archType", inputParam.getString("archType"));
        putIfNotEmpty(asicsObject, "asMark", inputParam.getString("asMark"));
        putIfNotEmpty(asicsObject, "atMark", inputParam.getString("atMark"));
        putIfNotEmpty(asicsObject, "birthday", inputParam.getString("birthday"));
        putIfNotEmpty(asicsObject, "bopenId", inputParam.getString("bopenId"));
        putIfNotEmpty(asicsObject, "cOpenID", inputParam.getString("cOpenID"));
        putIfNotEmpty(asicsObject, "city", inputParam.getString("city"));
        putIfNotEmpty(asicsObject, "county", inputParam.getString("county"));
        putIfNotEmpty(asicsObject, "email", inputParam.getString("email"));
        putIfNotEmpty(asicsObject, "extraGears", inputParam.getString("extraGears"));
        putIfNotEmpty(asicsObject, "growths", inputParam.getString("growths"));
        putIfNotEmpty(asicsObject, "height", inputParam.getString("height"));
//        putIfNotEmpty(asicsObject, "memberCode", inputParam.getString("memberCode"));
        putIfNotEmpty(asicsObject, "memberQr", inputParam.getString("memberQr"));
        putIfNotEmpty(asicsObject, "ownGears", inputParam.getString("ownGears"));
        putIfNotEmpty(asicsObject, "powers", inputParam.getString("powers"));
        putIfNotEmpty(asicsObject, "province", inputParam.getString("province"));
        putIfNotEmpty(asicsObject, "runningTag", inputParam.getString("runningTag"));
        putIfNotEmpty(asicsObject, "shoeSize", inputParam.getString("shoeSize"));
        putIfNotEmpty(asicsObject, "spotPreference", inputParam.getString("spotPreference"));
        putIfNotEmpty(asicsObject, "testResult", inputParam.getString("testResult"));
        putIfNotEmpty(asicsObject, "tmark", inputParam.getString("tmark"));
        putIfNotEmpty(asicsObject, "totalPowers", inputParam.getString("totalPowers"));
        putIfNotEmpty(asicsObject, "weekDistance", inputParam.getString("weekDistance"));
        putIfNotEmpty(asicsObject, "weight", inputParam.getString("weight"));
        putIfNotEmpty(asicsObject, "wxUserId", inputParam.getString("wxUserId"));
        
        // 数值字段
        if (inputParam.containsKey("caller") && inputParam.get("caller") != null) {
            asicsObject.put("caller", inputParam.getInteger("caller"));
        }

        // 渠道信息 - 使用基类方法设置
        setChannel(asicsObject, appCfgObj, inputParam);

        // 会员系统ID - 使用基类方法设置
        setMembershipSystemId(asicsObject, appCfgObj, inputParam);

        String result = asicsObject.toJSONString();
        return result;
    }
    
    /**
     * 辅助方法：如果值不为空则放入对象
     */
    private void putIfNotEmpty(JSONObject jsonObject, String key, String value) {
        if (StringUtils.isNotEmpty(value)) {
            jsonObject.put(key, value);
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memMdy.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberName", "糖晓甜");
        object.put("nickName", "糖x晓甜");
        object.put("gender", "F");
        object.put("address", "北京市朝阳区");
        object.put("height", "170");
        object.put("weight", "60");
        object.put("shoeSize", "40");
        object.put("runningTag", "初级跑者");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"gender\":\"女\",\"province\":\"北京市\",\"city\":\"朝阳区\",\"email\":\"<EMAIL>\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

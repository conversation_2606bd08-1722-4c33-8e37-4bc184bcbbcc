package com.shuyun.mariana.script.impl.asics.pointchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分返还响应体转换脚本
 * 功能：将ASICS系统的响应转换为内部标准积分返还响应协议
 */
public class MemberPointRevertRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("status","1");
        RestWrap<Map<String,Object>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(resultMap);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memberPointRevert.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "200");
        object.put("msg", "积分返还成功");
        object.put("reqId", "REVERT_REQ_" + System.currentTimeMillis());

        JSONObject dataObj = new JSONObject();
        dataObj.put("points", 50);
        dataObj.put("currentPoints", 1450);
        dataObj.put("transactionId", "REVERT_" + System.currentTimeMillis());
        dataObj.put("originalTransactionId", "TXN_" + (System.currentTimeMillis() - 3600000));
        dataObj.put("memberCode", "ASICS_CODE_001");
        dataObj.put("sourceType", 3);
        dataObj.put("obtainType", 3);
        dataObj.put("summary", "订单取消，返还积分");
        dataObj.put("channel", 15);
        dataObj.put("createTime", "2024-09-17 16:28:38");
        dataObj.put("revertReason", "ORDER_CANCEL");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

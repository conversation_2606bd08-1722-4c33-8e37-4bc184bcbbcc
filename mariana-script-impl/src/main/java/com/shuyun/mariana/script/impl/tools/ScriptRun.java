//package com.shuyun.mariana.script.impl.tools;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONObject;
//import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
//import com.shuyun.gateway.script.engine.script.MarianaScript;
//import com.shuyun.gateway.script.engine.script.ScriptFactory;
//import com.shuyun.gateway.script.engine.script.ScriptTypeEnum;
//import com.shuyun.mariana.script.impl.config.ConstantKey;
//import org.springframework.beans.BeanUtils;
//
//import java.util.HashMap;
//import java.util.Map;
//
///**
// * @Author: meng.lv
// * @Date: 2024/7/15 10:36
// */
//public class ScriptRun {
//    public static void main(String[] args) {
////        MemberRegReq memberRegReq = new MemberRegReq();
////        memberRegReq.setMobile("18710426216");
////
////        Map<String, String> map = new HashMap<>();
////        BeanUtils.copyProperties(memberRegReq, map);
////
////        System.out.println(map);
//
//
////        testReqBody();
//        testRspBody();
//    }
//
//
//    private static void testReqBody() {
//        String content = "import com.alibaba.fastjson.JSON;\n" +
//                "import com.alibaba.fastjson.JSONObject;\n" +
//                "import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;\n" +
//                "import java.text.SimpleDateFormat;\n" +
//                "import java.util.Date;\n" +
//                "\n" +
//                "import com.alibaba.fastjson.JSON;\n" +
//                "import com.alibaba.fastjson.JSONObject;\n" +
//                "import com.shuyun.cem.std.member.protocol.BaseMember;\n" +
//                "import com.shuyun.cem.std.member.protocol.RestWrap;\n" +
//                "import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;\n" +
//                "import java.util.Objects;\n" +
//                "import java.util.UUID;\n" +
//                "\n" +
//                "// 反序列化为协议\n" +
//                "MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);\n" +
//                "// 构建通用\n" +
//                "JSONObject jsonObject = buildCommon(memberRegReq);\n" +
//                "SimpleDateFormat formatter = new SimpleDateFormat(\"yyyy-MM-dd HH:mm:ss\");\n" +
//                "jsonObject.put(\"registerTime\", formatter.format(new Date()));\n" +
//                "// 会员基础业务数据填充\n" +
//                "buildMemberBiz(jsonObject, memberRegReq);\n" +
//                "String result = jsonObject.toJSONString();\n" +
//                "return result;\n" +
//                "\n" +
//                "public JSONObject buildCommon(BaseMember baseMember) {\n" +
//                "    // copy通用变量\n" +
//                "    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));\n" +
//                "    // 拼接公共参数\n" +
//                "    jsonObject.put(\"bizCode\", \"DVF\");\n" +
//                "    jsonObject.put(\"requestChannel\", \"WECHAT\");\n" +
//                "    jsonObject.put(\"requestSystem\", \"MEMBER_CENTER\");\n" +
//                "    jsonObject.put(\"transactionId\", UUID.randomUUID().toString());\n" +
//                "    jsonObject.put(\"scene\", \"mini_program\");\n" +
//                "    JSONObject identityObj = new JSONObject();\n" +
//                "    identityObj.put(\"userId\", baseMember.getAppId() + \"_\" + baseMember.getOpenId());\n" +
//                "    jsonObject.put(\"identify\", identityObj);\n" +
//                "    return jsonObject;\n" +
//                "}\n" +
//                "public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {\n" +
//                "    jsonObject.put(\"fullName\", memberRegReq.getMemberName());\n" +
//                "    jsonObject.put(\"nick\", memberRegReq.getNickname());\n" +
//                "    jsonObject.put(\"dateOfBirth\", memberRegReq.getBirthday());\n" +
//                "    // M:已婚 S:未婚 D:离异 O:其他\n" +
//                "    String marrage = \"O\";\n" +
//                "    // 0未婚1已婚2未知\n" +
//                "    if (Objects.isNull(memberRegReq.getMarriageStatus())) {\n" +
//                "        marrage = \"O\";\n" +
//                "    } else if (memberRegReq.getMarriageStatus().equals(2)) {\n" +
//                "        marrage = \"O\";\n" +
//                "    } else if (memberRegReq.getMarriageStatus().equals(0)) {\n" +
//                "        marrage = \"S\";\n" +
//                "    } else if (memberRegReq.getMarriageStatus().equals(1)) {\n" +
//                "        marrage = \"M\";\n" +
//                "    }\n" +
//                "    jsonObject.put(\"marriage\", marrage);\n" +
//                "}\n" +
//                "\n" +
//                "public String projectDir() {\n" +
//                "    return \"dvf\";\n" +
//                "}\n" +
//                "\n";
//        MarianaScript marianaScript = ScriptFactory.getScript(ScriptTypeEnum.groovy.name(), content);
//
//        JSONObject object = new JSONObject();
//        object.put("appId", "wxf492c06764b16035");
//        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
//        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
//        object.put("mobile", "18710426216");
//
//        Map<String, Object> param = new HashMap<>();
//        param.put("reqBodyInJson", object.toJSONString());
//        String result = (String) marianaScript.execute(param);
//        System.out.println(result);
//    }
//
//    private static void testRspBody() {
//        String content = "import com.alibaba.fastjson.JSON;\n" +
//                "import com.alibaba.fastjson.JSONObject;\n" +
//                "import com.shuyun.cem.std.member.protocol.BaseMember;\n" +
//                "import com.shuyun.cem.std.member.protocol.RestWrap;\n" +
//                "import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;\n" +
//                "import jdk.nashorn.internal.ir.annotations.Ignore;\n" +
//                "import java.util.Objects;\n" +
//                "import java.util.UUID;\n" +
//                "\n" +
//                "import com.alibaba.fastjson.JSON;\n" +
//                "import com.alibaba.fastjson.JSONObject;\n" +
//                "import com.shuyun.cem.std.member.protocol.RestWrap;\n" +
//                "import com.shuyun.cem.std.member.protocol.memreg.MemberRegRsp;\n" +
//                "import java.util.Objects;\n" +
//                "\n" +
//                "JSONObject rspObj = JSON.parseObject(rspBodyInJson);\n" +
//                "Boolean success = rspObj.getBoolean(\"success\");\n" +
//                "if (!success) {\n" +
//                "    RestWrap errRestWrap = new RestWrap();\n" +
//                "    errRestWrap.setSuccess(false);\n" +
//                "    errRestWrap.setCode(rspObj.getString(\"code\"));\n" +
//                "    errRestWrap.setMessage(rspObj.getString(\"message\"));\n" +
//                "    return JSON.toJSONString(errRestWrap);\n" +
//                "}\n" +
//                "return doRun(rspObj);\n" +
//                "\n" +
//                "\n" +
//                "public String doRun(JSONObject rspObj) {\n" +
//                "    MemberRegRsp memberRegRsp = new MemberRegRsp();\n" +
//                "    JSONObject dataObj = rspObj.getJSONObject(\"data\");\n" +
//                "    if (Objects.nonNull(dataObj)) {\n" +
//                "        memberRegRsp.setMemberId(dataObj.getString(\"memberId\"));\n" +
//                "        memberRegRsp.setStatus(dataObj.getString(\"status\"));\n" +
//                "    }\n" +
//                "    RestWrap<MemberRegRsp> regRspRestWrap = new RestWrap<>();\n" +
//                "    regRspRestWrap.buildSuccess(memberRegRsp);\n" +
//                "    String result = JSON.toJSONString(regRspRestWrap);\n" +
//                "    return result;\n" +
//                "}\n" +
//                "\n";
//        MarianaScript marianaScript = ScriptFactory.getScript(ScriptTypeEnum.groovy.name(), content);
//
//
//        String orderResultMock = "{\n" +
//                "    \"success\": true,\n" +
//                "    \"code\": \"000000\",\n" +
//                "    \"message\": \"成功\",\n" +
//                "    \"data\": {\n" +
//                "        \"page\": 0,\n" +
//                "        \"pageSize\": 20,\n" +
//                "        \"totalCount\": 2,\n" +
//                "        \"items\": [\n" +
//                "            {\n" +
//                "                \"shopTypeCode\": \"TAOBAO\",\n" +
//                "                \"channelType\": \"WECHAT\",\n" +
//                "                \"totalFee\": 90.0,\n" +
//                "                \"payment\": 80.0,\n" +
//                "                \"discountRate\": 0.85,\n" +
//                "                \"discountFee\": 15.0,\n" +
//                "                \"orderId\": \"087989aaf6af4e58897794d00002\",\n" +
//                "                \"shopCode\": \"SHOP001\",\n" +
//                "                \"shopName\": \"SHOP001\",\n" +
//                "                \"totalQuantity\": 1,\n" +
//                "                \"orderType\": \"NORMAL\",\n" +
//                "                \"orderStatus\": \"FINISHED\",\n" +
//                "                \"freight\": 10.0,\n" +
//                "                \"receiverName\": \"abc\",\n" +
//                "                \"receiverMobile\": \"18900000000\",\n" +
//                "                \"receiverTelephone\": \"18900000000\",\n" +
//                "                \"receiverProvince\": \"上海\",\n" +
//                "                \"receiverCity\": \"上海\",\n" +
//                "                \"receiverDistrict\": \"浦东新区\",\n" +
//                "                \"receiverAddress\": \"南京东路100号\",\n" +
//                "                \"receiverZipCode\": \"200000\",\n" +
//                "                \"couponFee\": 15.0,\n" +
//                "                \"guideCode\": \"001\",\n" +
//                "                \"description\": \"无\",\n" +
//                "                \"pointFlag\": 1,\n" +
//                "                \"isInternal\": \"N\",\n" +
//                "                \"isSend\": \"N\",\n" +
//                "                \"id\": \"087989aaf6af4e58897794d00002\",\n" +
//                "                \"payTime\": \"2024-07-17 14:48:50\",\n" +
//                "                \"finishTime\": \"2024-07-17 14:48:50\",\n" +
//                "                \"orderTime\": \"2024-07-17 14:48:50\",\n" +
//                "                \"lastSync\": \"2024-07-17 14:39:47\",\n" +
//                "                \"orderItems\": [\n" +
//                "                    {\n" +
//                "                        \"id\": \"bf3a654f1afa4859bb5c0e884600001\",\n" +
//                "                        \"shopTypeCode\": \"TAOBAO\",\n" +
//                "                        \"channelType\": \"WECHAT\",\n" +
//                "                        \"totalFee\": 90.0,\n" +
//                "                        \"payment\": 80.0,\n" +
//                "                        \"discountRate\": 0.85,\n" +
//                "                        \"discountFee\": 15.0,\n" +
//                "                        \"orderItemId\": \"bf3a654f1afa4859bb5c0e884600001\",\n" +
//                "                        \"productCode\": \"P001\",\n" +
//                "                        \"productName\": \"P001\",\n" +
//                "                        \"quantity\": 1,\n" +
//                "                        \"status\": \"FINISHED\",\n" +
//                "                        \"skuId\": \"S001\",\n" +
//                "                        \"tagPrice\": 100.0,\n" +
//                "                        \"retailPrice\": 100.0,\n" +
//                "                        \"orderType\": \"NORMAL\",\n" +
//                "                        \"picture\": [\n" +
//                "                            \"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"\n" +
//                "                        ],\n" +
//                "                        \"orderTime\": \"2024-07-17 14:48:50\",\n" +
//                "                        \"finishTime\": \"2024-07-17 14:48:50\",\n" +
//                "                        \"lastSync\": \"2024-07-17 14:39:47\",\n" +
//                "                        \"orderId\": \"087989aaf6af4e58897794d00002\",\n" +
//                "                        \"shopName\": \"SHOP001\",\n" +
//                "                        \"shopCode\": \"SHOP001\"\n" +
//                "                    }\n" +
//                "                ]\n" +
//                "            },\n" +
//                "            {\n" +
//                "                \"shopTypeCode\": \"TAOBAO\",\n" +
//                "                \"channelType\": \"WECHAT\",\n" +
//                "                \"totalFee\": 85.0,\n" +
//                "                \"payment\": 85.0,\n" +
//                "                \"discountRate\": 0.85,\n" +
//                "                \"discountFee\": 15.0,\n" +
//                "                \"orderId\": \"087989aaf6af4e58897794d18axxxxx\",\n" +
//                "                \"shopCode\": \"SHOP001\",\n" +
//                "                \"shopName\": \"SHOP001\",\n" +
//                "                \"totalQuantity\": 1,\n" +
//                "                \"orderType\": \"NORMAL\",\n" +
//                "                \"orderStatus\": \"FINISHED\",\n" +
//                "                \"freight\": 10.0,\n" +
//                "                \"receiverName\": \"abc\",\n" +
//                "                \"receiverMobile\": \"18900000000\",\n" +
//                "                \"receiverTelephone\": \"18900000000\",\n" +
//                "                \"receiverProvince\": \"上海\",\n" +
//                "                \"receiverCity\": \"上海\",\n" +
//                "                \"receiverDistrict\": \"浦东新区\",\n" +
//                "                \"receiverAddress\": \"南京东路100号\",\n" +
//                "                \"receiverZipCode\": \"200000\",\n" +
//                "                \"couponFee\": 15.0,\n" +
//                "                \"guideCode\": \"001\",\n" +
//                "                \"description\": \"无\",\n" +
//                "                \"pointFlag\": 1,\n" +
//                "                \"isInternal\": \"N\",\n" +
//                "                \"isSend\": \"N\",\n" +
//                "                \"id\": \"087989aaf6af4e58897794d18axxxxx\",\n" +
//                "                \"lastSync\": \"2024-07-09 18:41:32\",\n" +
//                "                \"orderItems\": [\n" +
//                "                    {\n" +
//                "                        \"id\": \"bf3a654f1afa4859bb5c0e8846bxxxxx\",\n" +
//                "                        \"shopTypeCode\": \"TAOBAO\",\n" +
//                "                        \"channelType\": \"WECHAT\",\n" +
//                "                        \"totalFee\": 85.0,\n" +
//                "                        \"payment\": 85.0,\n" +
//                "                        \"discountRate\": 0.85,\n" +
//                "                        \"discountFee\": 15.0,\n" +
//                "                        \"orderItemId\": \"bf3a654f1afa4859bb5c0e8846bxxxxx\",\n" +
//                "                        \"productCode\": \"P001\",\n" +
//                "                        \"productName\": \"P001\",\n" +
//                "                        \"quantity\": 1,\n" +
//                "                        \"status\": \"FINISHED\",\n" +
//                "                        \"skuId\": \"S001\",\n" +
//                "                        \"tagPrice\": 100.0,\n" +
//                "                        \"retailPrice\": 100.0,\n" +
//                "                        \"orderType\": \"NORMAL\",\n" +
//                "                        \"picture\": [\n" +
//                "                            \"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"\n" +
//                "                        ],\n" +
//                "                        \"lastSync\": \"2024-07-09 18:41:32\",\n" +
//                "                        \"orderId\": \"087989aaf6af4e58897794d18axxxxx\",\n" +
//                "                        \"shopName\": \"SHOP001\",\n" +
//                "                        \"shopCode\": \"SHOP001\"\n" +
//                "                    }\n" +
//                "                ]\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    }\n" +
//                "}";
//
//
//        JSONObject object = JSON.parseObject(orderResultMock);
////        object.put("success", true);
////        object.put("code", "testCode");
////        object.put("message", "testErrMsg");
////
////        JSONObject dataObj = new JSONObject();
////        dataObj.put("memberId", "DVF20240711WX00000");
////        dataObj.put("status", "REGISTERED");
////        object.put("data", dataObj);
//
//        Map<String, Object> param = new HashMap<>();
//        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
//        String result = (String) marianaScript.execute(param);
//        System.out.println(result);
//    }
//}

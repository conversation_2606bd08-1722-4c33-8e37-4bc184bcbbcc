package com.shuyun.mariana.script.impl.miele.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
public class MemberPointChangeRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<Map<String,Object>> restWrap = new RestWrap<>();
        Map<String,Object> resultMap = new HashMap<>();
        BigDecimal point = rspObj.getBigDecimal("point");
        if (point!=null){
            resultMap.put("status","1");
            resultMap.put("point",point);
            restWrap.buildSuccess(resultMap);
            String result = JSON.toJSONString(restWrap);
            return result;
        }else {
            String message = rspObj.getString("message");
            if (message!=null&&!"".equalsIgnoreCase(message)){
                RestWrap errRestWrap = new RestWrap();
                errRestWrap.setSuccess(false);
                errRestWrap.setCode(rspObj.getString("code"));
                errRestWrap.setMessage(message);
                return JSON.toJSONString(errRestWrap);
            }
            return JSON.toJSONString(restWrap);
        }
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memberPointChange.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject dataObj = new JSONObject();
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, dataObj.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
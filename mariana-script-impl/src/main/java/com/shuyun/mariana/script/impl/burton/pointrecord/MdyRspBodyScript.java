package com.shuyun.mariana.script.impl.burton.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        PageWrap<PointRecordDto> pageWrap = new PageWrap();
        ArrayList<PointRecordDto> pointRecordDtos = new ArrayList<>();
        JSONArray items = JSON.parseArray(rspBodyInJson);
        for (int i = 0; i < items.size(); i++) {
            JSONObject jsonObject = items.getJSONObject(i);
            PointRecordDto pointRecordDto = JSON.parseObject(JSON.toJSONString(jsonObject), PointRecordDto.class);
            pointRecordDto.setCurPoint(jsonObject.getDouble("point"));
            pointRecordDto.setTotalPoint(jsonObject.getDouble("totalPoint"));
            String changeType = jsonObject.getString("changeType");
            /**
             * 积分变更类型 SEND: 立即发放, DELAY_SEND: 延迟发放, EXPIRE: 过期, FREEZE: 冻结, UNFREEZE: 取消冻结, DEDUCT: 扣减, LOCK: 预 扣 ,
             * UNLOCK: 解 锁 , ABOLISH: 作 废 , TIMER: 定 时 , RECALCULATE: 废 弃 重 算 , SPECIAL_DEDUCT: 特 殊 扣 除 , SPECIAL_FREEZE: 特殊冻结,
             * SPECIAL_UNFREEZE: 特殊解冻, SPECIAL_ABOLISH: 特殊废弃, MANUAL_ABOLISH: 手动废弃
             */
            switch (changeType) {
                case "SEND":
                    pointRecordDto.setRecordType("add");
                    break;
                case "DELAY_SEND":
                    pointRecordDto.setRecordType("add");
                    break;
                case "EXPIRE":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "FREEZE":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "UNFREEZE":
                    pointRecordDto.setRecordType("add");
                    break;
                case "DEDUCT":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "LOCK":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "UNLOCK":
                    pointRecordDto.setRecordType("add");
                    break;
                case "ABOLISH":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "TIMER":
                    pointRecordDto.setRecordType("add");
                    break;
                case "RECALCULATE":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "SPECIAL_DEDUCT":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "SPECIAL_FREEZE":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "SPECIAL_UNFREEZE":
                    pointRecordDto.setRecordType("add");
                    break;
                case "SPECIAL_ABOLISH":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "MANUAL_ABOLISH":
                    pointRecordDto.setRecordType("minus");
                    break;
                case "REVERSE_SEND":
                    pointRecordDto.setRecordType("add");
                    break;
                default:
                    break;
            }
             pointRecordDto.setDescription(jsonObject.getString("description"));
            pointRecordDtos.add(pointRecordDto);
        }
        pageWrap.setItems(pointRecordDtos);
        RestWrap<PageWrap<PointRecordDto>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(restWrap);
        return result;
    }
    @Override
    public String groovyFileName() {
         return projectDir()+".pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[\n" +
                "    {\n" +
                "        \"memberId\": \"50cfc6def6c2421ab27467075f3bec6b\",\n" +
                "        \"point\": 20,\n" +
                "        \"totalPoint\": 40.0,\n" +
                "        \"changeType\": \"SEND\",\n" +
                "        \"description\": \"测试积分发送\",\n" +
                "        \"effectiveTime\": \"2025-04-22T14:42:35.179+08:00\",\n" +
                "        \"changeTime\": \"2025-04-22T14:42:37.195+08:00\",\n" +
                "        \"changeMode\": \"INTERFACE\",\n" +
                "        \"id\": \"75cc0b19802c46d28deb77abf24385e6\",\n" +
                "        \"traceId\": \"shuyun\",\n" +
                "        \"channel\": \"WECHAT\",\n" +
                "        \"key\": \"shuyun\",\n" +
                "        \"integral\": 20.0\n" +
                "    }\n" +
                "]");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

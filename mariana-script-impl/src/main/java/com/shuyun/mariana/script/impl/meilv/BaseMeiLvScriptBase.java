package com.shuyun.mariana.script.impl.meilv;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;


/**
 * 美旅小程序
 */
public abstract class BaseMeiLvScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        if (rspBodyInJson!=null&&!"".equalsIgnoreCase(rspBodyInJson)){
            String error_code = rspObj.getString("error_code");
            if (error_code!=null&&!"".equalsIgnoreCase(error_code)) {
                RestWrap errRestWrap = new RestWrap();
                errRestWrap.setSuccess(false);
                errRestWrap.setCode(rspObj.getString("error_code"));
                errRestWrap.setMessage(rspObj.getString("msg"));
                return JSON.toJSONString(errRestWrap);
            }
        }
        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }


    public JSONObject buildBirthday(String birthday,JSONObject jsonObject){
        if (birthday!=null&&!"".equalsIgnoreCase(birthday)){
            try {
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
                Date birthdayDate = formatter.parse(birthday);
                SimpleDateFormat formatterYear = new SimpleDateFormat("yyyy");
                SimpleDateFormat formatterDay = new SimpleDateFormat("MM-dd");
                jsonObject.put("birthDay", formatterDay.format(birthdayDate));
                jsonObject.put("birthYear", formatterYear.format(birthdayDate));
            }catch (Exception e){
            }
        }
        return jsonObject;
    }

    public JSONObject buildMemberType(BaseMember baseMember,JSONObject jsonObject){
        jsonObject.put("memberType", "samsonite");
        JSONObject appCfgJson = JSON.parseObject(baseMember.getAppCfgJson());
        if (Objects.nonNull(appCfgJson)) {
            String memberType = appCfgJson.getString("memberType");
            jsonObject.put("memberType", memberType);
        }
        return jsonObject;
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "meilv";
    }
}

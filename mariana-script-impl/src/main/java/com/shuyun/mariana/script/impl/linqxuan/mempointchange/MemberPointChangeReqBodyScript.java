package com.shuyun.mariana.script.impl.linqxuan.mempointchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

public class MemberPointChangeReqBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        BaseMember baseMember = JSON.parseObject(reqBodyInJson, BaseMember.class);
        JSONObject inputBody = JSON.parseObject(reqBodyInJson);
        JSONObject jsonObject = buildSimpleCommon(inputBody, baseMember);
        if (inputBody.get("pointChangeType")!=null&& !"".equals(inputBody.getString("pointChangeType"))){

            //会员基础业务数据填充
            buildMemberBiz(jsonObject, baseMember);

            String memberId = inputBody.getString("memberId");
            if (StringUtils.isNotEmpty(memberId)) {
                jsonObject.put("vipCode", memberId);
            } else {
                jsonObject.put("tel", baseMember.getMobile());
            }
            jsonObject.put("createType", inputBody.getString("source"));
            jsonObject.put("context", inputBody.getString("desc"));
            jsonObject.put("shopCode", "DZSW083");

            String pointChangeType = inputBody.getString("pointChangeType");

            BigDecimal changePoint = inputBody.getBigDecimal("changePoint");
            //积分发放
            if ("POINT_GAIN".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("point", changePoint);
            }
            //积分消费
            if ("POINT_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("point", changePoint.negate());
            }
            //解冻积分
            if ("POINT_UNFREEZE".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("point", changePoint);
            }
            //冻结积分
            if ("POINT_FREEZE".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("point", changePoint.negate());
            }
//            //冻结消费
//            if ("POINT_FREEZE_CONSUME".equalsIgnoreCase(pointChangeType)){
//                jsonObject.put("freezeTransactionId", jsonObject.getString("freezeId"));
//            }
            //返还消费积分
            if ("POINT_REVERT".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("point", changePoint);
            }
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("sequence", "sequence");
        object.put("expired", "expired");
        object.put("created", "created");
        object.put("openId", "id");
        object.put("source", "source");
        object.put("changePoint", 90);
        object.put("operator", "operator");
        object.put("desc", "desc");
        object.put("pointChangeType", "POINT_CONSUME");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
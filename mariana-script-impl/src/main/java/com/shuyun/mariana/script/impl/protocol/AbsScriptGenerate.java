package com.shuyun.mariana.script.impl.protocol;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.shuyun.gateway.script.engine.script.MarianaScript;
import com.shuyun.gateway.script.engine.script.ScriptFactory;
import com.shuyun.gateway.script.engine.script.ScriptTypeEnum;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.tools.ApiClient;
import com.shuyun.mariana.script.impl.tools.CodeParser;
import com.shuyun.mariana.script.impl.tools.ParseResultDto;
import com.shuyun.mariana.script.impl.tools.ScriptPO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @Author: meng.lv
 * @Date: 2024/7/23 9:55
 */
@Slf4j
public abstract class AbsScriptGenerate implements BaseScriptIfc {

    public abstract String run(String reqBodyInJson);

    @IgnoreGen
    public ScriptContext genReqBodyScript(Boolean uploadToApiPlat) {
        if (!getClass().getSimpleName().contains("ReqBody")) {
            throw new RuntimeException("此方法只能被用于生成请求Body脚本");
        }

        checkPkg();

        //1 描述源文件完整路径
        String baseDir = System.getProperty("user.dir");
        String dir = baseDir + ConstantKey.SCRIPT_CLASS_BASE_DIR;

        //2 获取类 import语句和run语句
        Class clazz = this.getClass();
        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, clazz, ImmutableMap.of("run", false), Sets.newHashSet());

        //3 获取父类 import语句 和 function
        Class pClazz = clazz.getSuperclass();
        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, pClazz, new HashMap<>(), Sets.newHashSet("run", "doRun"));

        //4 组装并生成文件
        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
        BaseScriptIfc baseScriptIfc = null;
        try {
            baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e.getMessage());
        }
        String fileName = genScript(scriptContent, baseScriptIfc);

        //5 上传api平台
        if (uploadToApiPlat) {
            ScriptPO scriptPO = new ScriptPO();
            scriptPO.setName(baseScriptIfc.groovyFileName());
            scriptPO.setBizType(baseScriptIfc.bizType());
            scriptPO.setScriptContent(scriptContent);
            ApiClient.uploadScript(scriptPO);
        }

        System.out.println(String.format("gen script:%s success, in dir:%s", fileName, projectDir()));

        ScriptContext scriptContext = new ScriptContext();
        scriptContext.setFileName(fileName);
        scriptContext.setContent(scriptContent);
        return scriptContext;
    }

    @IgnoreGen
    public ScriptContext genReqParamScript(Boolean uploadToApiPlat) {
        if (!getClass().getSimpleName().contains("ReqParam")) {
            throw new RuntimeException("此方法只能被用于生成请求Param脚本");
        }

        checkPkg();

        //1 描述源文件完整路径
        String baseDir = System.getProperty("user.dir");
        String dir = baseDir + ConstantKey.SCRIPT_CLASS_BASE_DIR;

        //2 获取类 import语句和run语句
        Class clazz = this.getClass();
        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, clazz, ImmutableMap.of("run", false), Sets.newHashSet());

        //3 获取父类 import语句 和 function
        Class pClazz = clazz.getSuperclass();
        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, pClazz, new HashMap<>(), Sets.newHashSet("run", "doRun"));

        //4 组装并生成文件
        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
        BaseScriptIfc baseScriptIfc = null;
        try {
            baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e.getMessage());
        }
        String fileName = genScript(scriptContent, baseScriptIfc);

        //5 上传api平台
        if (uploadToApiPlat) {
            ScriptPO scriptPO = new ScriptPO();
            scriptPO.setName(baseScriptIfc.groovyFileName());
            scriptPO.setBizType(baseScriptIfc.bizType());
            scriptPO.setScriptContent(scriptContent);
            ApiClient.uploadScript(scriptPO);
        }

        System.out.println(String.format("gen script:%s success, in dir:%s", fileName, projectDir()));

        ScriptContext scriptContext = new ScriptContext();
        scriptContext.setFileName(fileName);
        scriptContext.setContent(scriptContent);
        return scriptContext;
    }

    @IgnoreGen
    private String buildScriptContent(ParseResultDto parseResultDto, ParseResultDto pParseResultDto) {
        StringBuilder content = new StringBuilder("");
        if (StringUtils.isNotEmpty(parseResultDto.getImportStr())) {
            content.append(parseResultDto.getImportStr());
            content.append("\n");
        }

        if (StringUtils.isNotEmpty(pParseResultDto.getImportStr())) {
            content.append(pParseResultDto.getImportStr());
            content.append("\n");
        }

        if (StringUtils.isNotEmpty(parseResultDto.getFuncStr())) {
            content.append(parseResultDto.getFuncStr());
            content.append("\n");
        }

        if (StringUtils.isNotEmpty(pParseResultDto.getFuncStr())) {
            content.append(pParseResultDto.getFuncStr());
            content.append("\n");
        }

        return content.toString();
    }

    @IgnoreGen
    public String genScript(String scriptContent, BaseScriptIfc baseScriptIfc) {
        //指定目录，创建文件
        String dir = System.getProperty("user.dir");
        String path = dir + ConstantKey.SCRIPT_BASE_DIR + "\\" + projectDir();
        File proDir = new File(path);
        proDir.mkdir();
        String realPath = path;
        String fileName = baseScriptIfc.groovyFileName();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String curDate = formatter.format(new Date());
        fileName += "." + curDate + "." + System.currentTimeMillis();
        String filePath = realPath + "\\" + fileName;
        File file = new File(filePath);
        try {
            file.createNewFile();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        //写入文件
        try {
            Files.write(Paths.get(path, fileName), scriptContent.getBytes());
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }

        log.info("gen script success, fileName:{}, path:{}.", fileName, filePath);
        return fileName;
    }

    public abstract String projectDir();

    private void checkPkg() {
        String curPackage = getClass().getPackage().getName();
        String pPackage = getClass().getSuperclass().getPackage().getName();
        if (!curPackage.contains(pPackage)) {
            throw new RuntimeException("当前类需要继承本包下得通用基类");
        }
    }

    @IgnoreGen
    public ScriptContext genRspBodyScriptExtend(GenScriptParam genScriptParam) {
        if (!getClass().getSimpleName().contains("RspBody")) {
            throw new RuntimeException("此方法只能被用于生成响应脚本");
        }

        checkPkg();

        //1 描述源文件完整路径
        String baseDir = System.getProperty("user.dir");
        String dir = baseDir + ConstantKey.SCRIPT_CLASS_BASE_DIR;

        //2 获取父类 import语句和run语句
        Class clazz = this.getClass();
        Class pClazz = clazz.getSuperclass();
        Set<String> pExclude = Sets.newHashSet("doRun", "buildCommon", "buildMemberBiz");
        if (CollectionUtils.isNotEmpty(genScriptParam.getPExcludeFuncs())) {
            pExclude.addAll(genScriptParam.getPExcludeFuncs());
        }
        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, pClazz, genScriptParam.getPIncludeFuncs(), pExclude);

        //3 获取子类 import语句 和 function
        Map<String, Boolean> includeFuncs = new HashMap<>();
        includeFuncs.put("doRun", true);
        if (MapUtils.isNotEmpty(genScriptParam.getIncludeFuncs())) {
            includeFuncs.putAll(genScriptParam.getIncludeFuncs());
        }
        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, clazz, includeFuncs, Sets.newHashSet());

        //4 组装并生成文件
        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
        BaseScriptIfc baseScriptIfc = null;
        try {
            baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e.getMessage());
        }
        String fileName = genScript(scriptContent, baseScriptIfc);

        //5 上传api平台
        if (genScriptParam.getUploadToApiPlat()) {
            ScriptPO scriptPO = new ScriptPO();
            scriptPO.setName(baseScriptIfc.groovyFileName());
            scriptPO.setBizType(baseScriptIfc.bizType());
            scriptPO.setScriptContent(scriptContent);
            ApiClient.uploadScript(scriptPO);
        }

        System.out.println(String.format("gen script:%s success, in dir:%s", fileName, projectDir()));
        ScriptContext scriptContext = new ScriptContext();
        scriptContext.setFileName(fileName);
        scriptContext.setContent(scriptContent);
        return scriptContext;
    }

    @IgnoreGen
    public ScriptContext genRspBodyScript(Boolean uploadToApiPlat) {
        if (!getClass().getSimpleName().contains("RspBody")) {
            throw new RuntimeException("此方法只能被用于生成响应脚本");
        }

        checkPkg();

        //1 描述源文件完整路径
        String baseDir = System.getProperty("user.dir");
        String dir = baseDir + ConstantKey.SCRIPT_CLASS_BASE_DIR;

        //2 获取父类 import语句和run语句
        Class clazz = this.getClass();
        Class pClazz = clazz.getSuperclass();
        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, pClazz, ImmutableMap.of("run", false), Sets.newHashSet("doRun", "buildCommon", "buildMemberBiz"));

        //3 获取子类 import语句 和 function
        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, clazz, ImmutableMap.of("doRun", true), Sets.newHashSet());

        //4 组装并生成文件
        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
        BaseScriptIfc baseScriptIfc = null;
        try {
            baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e.getMessage());
        }
        String fileName = genScript(scriptContent, baseScriptIfc);

        //5 上传api平台
        if (uploadToApiPlat) {
            ScriptPO scriptPO = new ScriptPO();
            if (!baseScriptIfc.groovyFileName().contains(projectDir())) {
                throw new RuntimeException("请检查文件名，文件名必须以projectDir()为前缀");
            }
            scriptPO.setName(baseScriptIfc.groovyFileName());
            scriptPO.setBizType(baseScriptIfc.bizType());
            scriptPO.setScriptContent(scriptContent);
            ApiClient.uploadScript(scriptPO);
        }

        System.out.println(String.format("gen script:%s success, in dir:%s", fileName, projectDir()));
        ScriptContext scriptContext = new ScriptContext();
        scriptContext.setFileName(fileName);
        scriptContext.setContent(scriptContent);
        return scriptContext;
    }

    @Override
    @IgnoreGen
    public void testScript(ScriptContext scriptContext) {
        MarianaScript marianaScript = ScriptFactory.getScript(ScriptTypeEnum.groovy.name(), scriptContext.getContent(), scriptContext.getFileName());
        String result = (String) marianaScript.execute(scriptInputParam());
        System.out.println("before script modify, param:" + scriptInputParam().values().iterator().next());
        System.out.println("after script modify, param:" + result);
    }

    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        return new HashMap<>();
    }

    @IgnoreGen
    public static AbsScriptGenerate buildInstance() throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        String className = Thread.currentThread().getStackTrace()[2].getClassName();
        System.out.println("当前类的名称是: " + className);
        Class clazz = Class.forName(className);
        AbsScriptGenerate absScriptGenerate = (AbsScriptGenerate) clazz.newInstance();
        return absScriptGenerate;
    }
}

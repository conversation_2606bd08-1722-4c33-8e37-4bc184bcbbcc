package com.shuyun.mariana.script.impl.burton.memberduepoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


public class MdyReqParamScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);
        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        addParameters.put("memberId", jsonObject.getString("memberId"));
        String appCfgJson = jsonObject.getString("appCfgJson");
        if (appCfgJson!=null&&!"".equalsIgnoreCase(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            if (Objects.nonNull(appCfgObj)) {
                addParameters.put("memberType", appCfgObj.getString("memberType"));
                addParameters.put("channelType", appCfgObj.getString("channelType"));
            }
        }
        addParameters.put("pointBizType", "POINT");
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);
        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memberDuePointV2.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        HashMap<String, Object> map = new HashMap<>();
        map.put("currGradeName","绿道滑手");
        object.put("bizExtJson", JSON.toJSONString(map));
        object.put("memberId", "50cfc6def6c2421ab27467075f3bec6b");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.ccmsv2.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.ccmsv2.BaseCcmsV2ScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=238
 */
public class MdyReqBodyScript extends BaseCcmsV2ScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        //构建通用
        JSONObject jsonObject = buildCommon(memberDetailReq);

        String bizExtJson = memberDetailReq.getBizExtJson();
        String queryType = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            JSONObject bizJson = JSON.parseObject(bizExtJson);
            queryType = bizJson.getString("queryType");
        }

        //这里为了保持业务侧调用一致
        //memberId
        String memberId = inputParam.getString("memberId");
        if (StringUtils.isNotEmpty(memberId)) {
            jsonObject.put("id", memberId);
            jsonObject.put("type", "memberId");
        } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile")) {
            jsonObject.put("id", memberDetailReq.getMobile());
            jsonObject.put("type", "mobile");
        } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("unionId")) {
            jsonObject.put("id", memberDetailReq.getUnionId());
            jsonObject.put("type", "unionId");
        } else {
            //默认用appId_openId查询
            //平台客户账号
            jsonObject.put("id", memberDetailReq.getOpenId());
            jsonObject.put("type", "openId");
        }

        //填充项目配置
        buildMemQuery(jsonObject, memberDetailReq);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memDetail.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, "{\n" +
                "    \"type\": \"openId\",\n" +
                "    \"id\": \"tz平台账号1\",\n" +
                "    \"tenant\": \"sjyj\",\n" +
                "    \"cardPlanId\": 1100,\n" +
                "    \"shopId\": \"wxaada9391b766b633\"\n" +
                "}");
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

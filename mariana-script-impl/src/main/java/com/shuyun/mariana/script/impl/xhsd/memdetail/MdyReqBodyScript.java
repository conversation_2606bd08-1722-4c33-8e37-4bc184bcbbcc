package com.shuyun.mariana.script.impl.xhsd.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseXhsdScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyIn<PERSON>son, MemberDetailReq.class);
        //反序列化扩展字段
        JSONObject extObj = JSON.parseObject(memberDetailReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            Boolean kylinOpenIdQuery = extObj.getBoolean("kylinOpenIdQuery");
            ////通过openId查询，不需要UnionId查询
            if (kylinOpenIdQuery!=null&&kylinOpenIdQuery){
                memberDetailReq.setUnionId(null);
            }
            //通过UnionId 不需要openid查询
            if (kylinOpenIdQuery!=null&& !kylinOpenIdQuery){
                memberDetailReq.setOpenId(null);
            }
            String queryType = extObj.getString("queryType");
            if("mobile".equalsIgnoreCase(queryType) || "memberId".equalsIgnoreCase(queryType) ){
                memberDetailReq.setUnionId(null);
                memberDetailReq.setOpenId(null);
            }
        }
        JSONObject jsonObject = buildCommon(memberDetailReq);
        if (Objects.nonNull(extObj)) {
            String queryType = extObj.getString("queryType");
            String mobile = extObj.getString("mobile");
            String memberId = extObj.getString("memberId");
            if("mobile".equalsIgnoreCase(queryType)){
                jsonObject.put("mobile", mobile);
            }
            if("memberId".equalsIgnoreCase(queryType)){
                JSONObject identityObj = new JSONObject();
                identityObj.put("memberId", memberId);
                jsonObject.put("identify", identityObj);
            }
        }
        List<String> optionalFields = new ArrayList<>();
        optionalFields.add("parentMemberId");
        optionalFields.add("serviceGuide");
        optionalFields.add("serviceShopCode");
        optionalFields.add("serviceShopName");
        jsonObject.put("optionalFields", optionalFields);
        jsonObject.put("channelDataRequired", true);
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memDetail.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

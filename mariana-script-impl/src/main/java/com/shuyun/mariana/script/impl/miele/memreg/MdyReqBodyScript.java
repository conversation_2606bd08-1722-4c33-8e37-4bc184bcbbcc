package com.shuyun.mariana.script.impl.miele.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseMieleScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberRegReq);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        jsonObject.put("enrollTime", formatter.format(new Date()));
        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberRegReq);
        JSONObject extObj = JSON.parseObject(memberRegReq.getBizExtJson());
        String gender=jsonObject.getString("gender");
        if (gender==null || "".equalsIgnoreCase(gender)){
            //反序列化扩展字段
            if (Objects.nonNull(extObj)) {
                String genderStr = extObj.getString("gender");
                jsonObject.put("gender", genderStr);
            }
        }
        JSONObject extras=new JSONObject();
        //导购传参
        if (Objects.nonNull(extObj)) {
            String enrollShopCodeReq = extObj.getString("enrollShopCode");
            String enrollShopNameReq = extObj.getString("enrollShopName");
            String enrollGuideReq = extObj.getString("enrollGuide");
            if (enrollShopCodeReq!=null&&!"".equalsIgnoreCase(enrollShopCodeReq)){
                jsonObject.put("enrollShopCode", enrollShopCodeReq);
            }
            if (enrollShopNameReq!=null&&!"".equalsIgnoreCase(enrollShopNameReq)){
                jsonObject.put("enrollShopName", enrollShopNameReq);
            }
            if (enrollGuideReq!=null&&!"".equalsIgnoreCase(enrollGuideReq)){
                extras.put("enrollGuide", enrollGuideReq);
            }
        }
        String tId=null;
        if (Objects.nonNull(extObj)) {
             tId = extObj.getString("tId");
        }
        //参数码传参

        if ("0".equalsIgnoreCase(tId)){
            String param = extObj.getString("param");
            if (param!=null&&!"".equalsIgnoreCase(param)){
                JSONArray array = JSON.parseArray(param);
                if (array!=null){
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject jsonObj = array.getJSONObject(i);
                        String customCode=jsonObj.getString("customCode");
                        if ("SHOP_CODE".equalsIgnoreCase(customCode)){
                            String customName=jsonObj.getString("customName");
                            String customValue=jsonObj.getString("customValue");
                            jsonObject.put("enrollShopCode", customValue);
                            jsonObject.put("enrollShopName", customName);
                        }
                        if ("isvArea".equalsIgnoreCase(customCode)){
                            String customValue=jsonObj.getString("customValue");
                            extras.put("engineerArea", customValue);
                        }
                        if ("isv".equalsIgnoreCase(customCode)){
                            String customName=jsonObj.getString("customName");
                            String customValue=jsonObj.getString("customValue");
                            extras.put("engineerCode", customValue);
                            extras.put("engineerName", customName);
                        }
                    }
                }
            }
        }
        jsonObject.put("extras", extras);
        String enrollShopCode = jsonObject.getString("enrollShopCode");
        if (enrollShopCode==null || "".equalsIgnoreCase(enrollShopCode)){
            jsonObject.put("enrollShopCode", "MIELEMP");
            jsonObject.put("enrollShopName", "德国美诺Miele会员中心小程序");
        }

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
      //  object.put("bizExtJson","{\"tId\":\"0\",\"param\":[{\"customType\":\"POI\",\"customValue\":\"4459\",\"customName\":\"数云食堂朱雀西安南站店\",\"customCode\":\"SHOP_CODE\"},{\"customType\":\"CUSTOM\",\"customValue\":\"12222\",\"customName\":\"店铺\",\"customCode\":\"code\"}]}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

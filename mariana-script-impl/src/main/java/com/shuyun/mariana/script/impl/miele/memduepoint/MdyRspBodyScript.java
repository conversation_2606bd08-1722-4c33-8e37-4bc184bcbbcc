package com.shuyun.mariana.script.impl.miele.memduepoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String run(String reqBodyInJson) {
         System.out.println(reqBodyInJson);
        //非标准接口，直接是数据 且是json数组
        Map<String,Object> resultMap=new HashMap<>();
        Integer totalPoint=0;
        if (reqBodyInJson!=null&&!"".equalsIgnoreCase(reqBodyInJson)){
            JSONArray jsonArray = JSONArray.parseArray(reqBodyInJson);
            if (jsonArray!=null&&jsonArray.size()>0){
                for (int i=0;i<jsonArray.size();i++){
                    JSONObject jsonObj = jsonArray.getJSONObject(i);
                    Integer point = jsonObj.getInteger("point");
                    if (point!=null){
                        totalPoint=totalPoint+point;
                    }
                }
            }
        }
        resultMap.put("point",totalPoint);
        RestWrap<Map<String,Object>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultMap);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }
    @Override
    public String groovyFileName() {
        return projectDir()+".memDuePoint.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {

        JSONArray array=new JSONArray();
        JSONObject object = new JSONObject();
        object.put("point", 10);
        array.add(object);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, JSON.toJSONString(array));
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

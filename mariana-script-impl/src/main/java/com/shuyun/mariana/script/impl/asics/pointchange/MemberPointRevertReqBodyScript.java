package com.shuyun.mariana.script.impl.asics.pointchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分返还请求体转换脚本
 * 功能：将内部标准积分返还协议转换为ASICS系统所需的字段格式
 */
public class MemberPointRevertReqBodyScript extends BaseAsicsScriptBase {

    /**
     * 生成随机3位数（100-999）
     */
    private String generateRandom3Digits() {
        return String.valueOf(ThreadLocalRandom.current().nextInt(100, 1000));
    }

    @Override
    public String run(String reqBodyInJson) {
        JSONObject bodyParam = JSON.parseObject(reqBodyInJson);
        JSONObject asicsObject = new JSONObject();

        // 解析配置信息
        String appCfgJson = bodyParam.getString("appCfgJson");
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }

        String bizExtJson = bodyParam.getString("bizExtJson");
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 会员标识 - 优先使用memberId作为memberCode
        String memberId = bodyParam.getString("memberId");
        if (StringUtils.isNotEmpty(memberId)) {
            asicsObject.put("memberCode", memberId);
        } else if (bizExtObj != null && StringUtils.isNotEmpty(bizExtObj.getString("memberCode"))) {
            asicsObject.put("memberCode", bizExtObj.getString("memberCode"));
        }

        // 积分数量 - 返还积分通常为正数
        BigDecimal changePoint = bodyParam.getBigDecimal("changePoint");
        if (changePoint != null) {
            // 确保返还积分为正数
            asicsObject.put("points", changePoint.abs().intValue());
        }

        // 描述信息
        String desc = bodyParam.getString("desc");
        if (StringUtils.isNotEmpty(desc)) {
            asicsObject.put("summary", desc);
        } else {
            asicsObject.put("summary", "积分返还");
        }

        // 交易ID - 返还通常关联原交易
        /**
         * 事务Id：R开头+正向事务id+下划线（1-3数字不要重
         * 复，确保幂等），列：正向的事务id=12345 逆向的⾸次
         * 回退：R12345_123，第⼆次R12345_456,
         * 由业务侧传递
         */
        String transactionId = bodyParam.getString("transactionId");
        if (StringUtils.isEmpty(transactionId)) {
            transactionId = "R" + bodyParam.getString("freezeId") + "_" + generateRandom3Digits();
        }
        asicsObject.put("transactionId", transactionId);

        // 积分返还类型映射
        asicsObject.put("sourceType", 7); // 积分来源类型：7:POS抵扣、回退
        asicsObject.put("obtainType", 3); // 0, "系统入账"
        // 从输入参数中直接获取ASICS特有字段
        if (bodyParam.containsKey("sourceType")) {
            asicsObject.put("sourceType", bodyParam.getInteger("sourceType"));
        }
        if (bodyParam.containsKey("obtainType")) {
            asicsObject.put("obtainType", bodyParam.getInteger("obtainType"));
        }

        // 设置渠道信息和会员系统ID - 使用基类方法
        setChannel(asicsObject, appCfgObj, bodyParam);
        setMembershipSystemId(asicsObject, appCfgObj, bodyParam);

        String result = asicsObject.toJSONString();
        return result;
    }

    /**
     * 映射积分返还类型到ASICS字段
     */
    private void mapPointRevertType(JSONObject asicsObject, String pointChangeType) {
        switch (pointChangeType.toUpperCase()) {
            case "POINT_REVERT":
                // 积分返还/退款
                asicsObject.put("sourceType", 7); // 积分来源类型：7:POS抵扣、回退
                asicsObject.put("obtainType", 3); // 0, "系统入账"
                break;
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memberPointRevert.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("changePoint", 50);
        object.put("desc", "订单取消，返还积分");
        object.put("sequence", "REVERT_" + ThreadLocalRandom.current().nextInt(100, 1000));
        object.put("originalTransactionId", "TXN_" + ThreadLocalRandom.current().nextInt(100, 1000));
        object.put("pointChangeType", "POINT_REVERT");
        object.put("source", "ORDER_CANCEL");
        object.put("operator", "SYSTEM");
        object.put("sourceType", 3);
        object.put("obtainType", 3);
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"memberCode\":\"ASICS_CODE_001\",\"businessType\":\"ORDER_CANCEL\",\"originalOrderId\":\"ORDER_001\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

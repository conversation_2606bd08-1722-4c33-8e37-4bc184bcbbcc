package com.shuyun.mariana.script.impl.miele.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String doRun(JSONObject rspObj)  {
        JSONObject resultObj=new JSONObject();
        JSONArray items = rspObj.getJSONArray("content");
        if (items!=null){
            for (int i = 0; i < items.size(); i++) {
                JSONObject jsonObj = items.getJSONObject(i);
                try {
                    String changeTime = jsonObj.getString("created");
                    if (changeTime!=null){
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                        Date formattedDate = dateFormat.parse(changeTime);
                        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                        String formattedDateStr = dateFormat2.format(formattedDate);
                        jsonObj.put("changeTime",formattedDateStr);
                    }
                    String desc = jsonObj.getString("desc");
                    jsonObj.put("description",desc);
                    String effectiveDate = jsonObj.getString("effectiveDate");
                    jsonObj.put("effectTime",effectiveDate);
                    String overdueDate = jsonObj.getString("overdueDate");
                    jsonObj.put("expiredTime",overdueDate);
                }catch (Exception e){

                }
            }
        }else {
            items=new JSONArray();
        }
        resultObj.put("items",items);
        resultObj.put("totalPage",rspObj.getInteger("totalPages"));
        resultObj.put("totalCount",rspObj.getInteger("totalElements"));

        RestWrap<Map<String,Object>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(resultObj);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
         return projectDir()+".pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("recordType", "SEND");
        itemObj.put("point", 500.0);

        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);

        JSONObject pageObj = new JSONObject();
        pageObj.put("page", 0);
        pageObj.put("pageSize", 20);
        pageObj.put("totalCount", 2);
        pageObj.put("content", itemArr);


        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, pageObj.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;


/**
 * burton小程序
 */
public abstract class BaseBurtonScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        if (rspBodyInJson != null && !"".equalsIgnoreCase(rspBodyInJson)) {
            String error_code = rspObj.getString("error_code");
            if (error_code != null && !"".equalsIgnoreCase(error_code)) {
                RestWrap errRestWrap = new RestWrap();
                errRestWrap.setSuccess(false);
                errRestWrap.setCode(rspObj.getString("error_code"));
                errRestWrap.setMessage(rspObj.getString("msg"));
                return JSON.toJSONString(errRestWrap);
            }
        }
        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "burton";
    }
}

package com.shuyun.mariana.script.impl.burton.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap<OrderDto> pageWrap = new PageWrap();
        List<OrderDto> orderDtos = new ArrayList<>();
            pageWrap.setTotalCount(rspObj.getInteger("totalCount"));
            JSONArray data = rspObj.getJSONArray("data");
            if (Objects.nonNull(data)) {
                for (int i = 0; i < data.size(); i++) {
                    JSONObject orderObj = data.getJSONObject(i);
                    OrderDto orderDto = new OrderDto();
                    orderDto.setProjectExt(JSON.toJSONString(orderObj));
                    orderDtos.add(orderDto);
                }
            }
        pageWrap.setItems(orderDtos);
        RestWrap<PageWrap<OrderDto>> restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memOrder.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {

        JSONObject object = new JSONObject();
        object.put("currentPage", 1);
        object.put("pageSize", 20);
        object.put("totalCount", 1);
        object.put("totalPage", 1);
        object.put("data", new Object[1]);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.meilv.memgrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memgrade.MemberGradeQueryRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMeiLvScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        //非标准接口，直接是数据
        MemberGradeQueryRsp memberGradeQueryRsp = new MemberGradeQueryRsp();
        if (Objects.nonNull(rspObj)) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
            SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            JSONObject gradeInfo = rspObj.getJSONObject("gradeInfo");
            String id = gradeInfo.getString("id");
            String name = gradeInfo.getString("name");
            if ("60005".equalsIgnoreCase(id)){
                name="启航旅行家";
            }
            if ("60006".equalsIgnoreCase(id)){
                name="先锋旅行家";
            }
            if ("60007".equalsIgnoreCase(id)){
                name="达人旅行家";
            }
            if ("60008".equalsIgnoreCase(id)){
                name="明星旅行家";
            }
            memberGradeQueryRsp.setGradeId(id);
            memberGradeQueryRsp.setGradeName(name);
            JSONObject projectExt =new JSONObject();
            projectExt.put("name",gradeInfo.getString("name"));
            memberGradeQueryRsp.setProjectExt(JSON.toJSONString(projectExt));
            try {
                String effectiveTime = gradeInfo.getString("effectiveTime");
                if (effectiveTime!=null){
                    Date effectiveTimeDate = dateFormat.parse(effectiveTime);
                    String effectiveTimeStr = dateFormat2.format(effectiveTimeDate);
                    memberGradeQueryRsp.setEffectTime(effectiveTimeStr);
                }

                String expiredTime = gradeInfo.getString("expiredTime");
                if (expiredTime!=null){
                    Date expiredTimeDate = dateFormat.parse(expiredTime);
                    String expiredTimeStr = dateFormat2.format(expiredTimeDate);
                    memberGradeQueryRsp.setExpiredTime(expiredTimeStr);
                }
            }catch (Exception e){

            }
        }
        RestWrap<MemberGradeQueryRsp> restWrap = new RestWrap<>();
        restWrap.buildSuccess(memberGradeQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memGrade.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {

        JSONObject dataObj = new JSONObject();
        JSONObject gradeInfo = new JSONObject();
        gradeInfo.put("id","1");
        gradeInfo.put("name","vpi");


        dataObj.put("gradeInfo", gradeInfo);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, dataObj.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

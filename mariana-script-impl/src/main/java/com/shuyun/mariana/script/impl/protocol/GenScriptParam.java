package com.shuyun.mariana.script.impl.protocol;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/30 16:47
 */
@Data
public class GenScriptParam {
    private Boolean uploadToApiPlat;
    private Map<String, Boolean> pIncludeFuncs = new HashMap<>();
    private List<String> pExcludeFuncs = new ArrayList<>();
    private Map<String, Boolean> includeFuncs = new HashMap<>();
    private List<String> excludeFuncs = new ArrayList<>();

    public void addPExcludeFunc(String func) {
        this.pExcludeFuncs.add(func);
    }

    public void addPIncludeFunc(String func, Boolean methodComplete) {
        this.pIncludeFuncs.put(func, methodComplete);
    }

    public void addIncludeFunc(String func, Boolean methodComplete) {
        this.includeFuncs.put(func, methodComplete);
    }

    public void addExcludeFunc(String func) {
        this.excludeFuncs.add(func);
    }
}

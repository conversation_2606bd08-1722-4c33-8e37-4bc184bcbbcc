package com.shuyun.mariana.script.impl.whoo.memtag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.cem.std.member.protocol.memtag.querymemtag.MemTagQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseWhooScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        JSONObject memTagQueryReq = JSON.parseObject(reqBodyInJson);
        //构建通用
        JSONObject jsonObject = new JSONObject();

        //反序列化 ext_cfg，得到卡计划ID
        String appCfgJson = memTagQueryReq.getString("appCfgJson");
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            jsonObject.put("programCode", appCfgObj.getString("programCode"));
            jsonObject.put("channelType", appCfgObj.getString("channel"));
        }

        jsonObject.put("memberId", memTagQueryReq.getString("memberId"));
        jsonObject.put("tagIds", memTagQueryReq.getJSONArray("tagIds"));
        jsonObject.put("page", memTagQueryReq.getIntValue("page"));
        jsonObject.put("pageSize", memTagQueryReq.getIntValue("pageSize"));

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memTag.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("bizExtJson","{\"gender\":\"M\",\"registerGuide\":\"A99999\",\"registerGuideName\":\"数云业务部-丽丽\",\"registerShopCode\":\"SHOP002\",\"registerShopName\":\"SHOP002\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

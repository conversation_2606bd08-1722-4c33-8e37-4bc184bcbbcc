package com.shuyun.mariana.script.impl.burton.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointConsumeReq;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointRevertReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MemberPointRevertReqBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        MemberPointRevertReq memberPointRevertReq = JSON.parseObject(reqBodyInJson, MemberPointRevertReq.class);
        //拼接公共参数
        JSONObject parseObject = JSON.parseObject(memberPointRevertReq.getBizExtJson());
        JSONObject object = new JSONObject();
        object.put("channelType", "WECHAT");
        object.put("desc", memberPointRevertReq.getDesc());
        object.put("memberId", jsonObject.getString("memberId"));
        object.put("memberType", "BURTON");
        object.put("shopCode",memberPointRevertReq.getShopId());
        object.put("recordType", "DEDUCT");
        object.put("uniqueId",jsonObject.getString("sequence"));
        object.put("pointAccountId","60064");
        object.put("tradeId",memberPointRevertReq.getFreezeId());
        if (Objects.nonNull(parseObject)) {
            object.put("effectTime",parseObject.get("created"));
            object.put("expiredTime",parseObject.get("expired"));
        }
        String result = object.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memberPointRevert.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
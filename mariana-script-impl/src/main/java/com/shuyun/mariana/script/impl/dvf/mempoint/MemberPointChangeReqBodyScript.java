package com.shuyun.mariana.script.impl.dvf.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class MemberPointChangeReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        //拼接公共参数
        String appCfgJson = jsonObject.getString("appCfgJson");
        if (appCfgJson!=null&&!"".equalsIgnoreCase(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            jsonObject.put("bizCode", appCfgObj.getString("bizCode"));
        } else {
            jsonObject.put("bizCode", "DVF");
        }
        jsonObject.put("requestChannel", "WECHAT");
        jsonObject.put("requestSystem", "MEMBER_CENTER");
        jsonObject.put("transactionId", jsonObject.getString("sequence"));
        jsonObject.put("scene", "mini_program");
        JSONObject identityObj = new JSONObject();
        identityObj.put("userId", jsonObject.getString("appId") + "_" + jsonObject.getString("openId"));
        jsonObject.put("identify", identityObj);
        jsonObject.put("description", jsonObject.getString("desc"));
        String bizExtJson=jsonObject.getString("bizExtJson");
        JSONObject extObj = JSON.parseObject(bizExtJson);
        if (Objects.nonNull(extObj)) {
            String pointBizTypeStr = extObj.getString("pointBizType");
            if (pointBizTypeStr==null || "".equalsIgnoreCase(pointBizTypeStr)){
                pointBizTypeStr="POINT";
            }
            jsonObject.put("pointBizType", pointBizTypeStr);
        }else {
            jsonObject.put("pointBizType", "POINT");
        }
        //    POINT_GAIN("积分发放"),
        //    POINT_CONSUME("积分消费"),
        //    POINT_REVERT("返还消费积分"),
        //    POINT_FREEZE("冻结积分"),
        //    POINT_UNFREEZE("解冻积分"),
        //    POINT_FREEZE_CONSUME("冻结消费");

        jsonObject.put("point", jsonObject.getBigDecimal("changePoint"));
        if (jsonObject.get("pointChangeType")!=null&& !"".equals(jsonObject.getString("pointChangeType"))){
            String pointChangeType = jsonObject.getString("pointChangeType");
            //积分发放
            if ("POINT_GAIN".equalsIgnoreCase(pointChangeType)){
                String created = jsonObject.getString("created");
                String expired = jsonObject.getString("expired");
                if (created!=null&&!"".equalsIgnoreCase(created)){
                    jsonObject.put("effectTime", created);
                }
                if (expired!=null&&!"".equalsIgnoreCase(expired)){
                    jsonObject.put("expiredTime", expired);
                }
                jsonObject.put("modifyType", "SEND");
            }
            //积分消费
            if ("POINT_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("modifyType", "DEDUCT");
            }
            //解冻积分
            if ("POINT_UNFREEZE".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("freezeTransactionId", jsonObject.getString("freezeId"));
            }
            //冻结积分
            if ("POINT_FREEZE".equalsIgnoreCase(pointChangeType)){

            }
            //冻结消费
            if ("POINT_FREEZE_CONSUME".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("freezeTransactionId", jsonObject.getString("freezeId"));
            }
            //返还消费积分
            if ("POINT_REVERT".equalsIgnoreCase(pointChangeType)){
                jsonObject.put("originalTransactionId", jsonObject.getString("freezeId"));
                jsonObject.put("revertType","DEDUCT");
            }
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("sequence", "sequence");
        object.put("expired", "expired");
        object.put("created", "created");
        object.put("openId", "id");
        object.put("source", "source");
        object.put("changePoint", 90);
        object.put("operator", "operator");
        object.put("desc", "desc");
        object.put("pointChangeType", "POINT_CONSUME");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
package com.shuyun.mariana.script.impl.miele.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONArray dataObj = rspObj.getJSONArray("data");
        List<JSONObject> resultJsonArray = new ArrayList<>();
        if (dataObj!=null) {
            for (int i = 0; i < dataObj.size(); i++) {
                JSONObject jsonObj = dataObj.getJSONObject(i);
                JSONObject jsonObjData=new JSONObject();
                jsonObjData.put("gradeId",jsonObj.getString("code"));
                jsonObjData.put("gradeName",jsonObj.getString("name"));
                try {
                    jsonObjData.put("gradeSort",jsonObj.getInteger("code"));
                }catch (Exception e){
                }
                resultJsonArray.add(jsonObjData);
            }
        }
        RestWrap<List<JSONObject>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultJsonArray);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        JSONArray jsonArray = new JSONArray();
        JSONObject gradeObj = new JSONObject();
        gradeObj.put("name", "test1");
        gradeObj.put("code", "1");
        jsonArray.add(gradeObj);
        object.put("data", jsonArray);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

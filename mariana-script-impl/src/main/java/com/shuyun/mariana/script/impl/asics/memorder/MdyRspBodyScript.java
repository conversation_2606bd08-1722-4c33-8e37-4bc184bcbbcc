package com.shuyun.mariana.script.impl.asics.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员订单查询响应体转换脚本
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<PageWrap<OrderDto>> restWrap = new RestWrap<>();

        // 处理ASICS订单响应数据
        JSONObject dataObj = rspObj.getJSONObject("data");
        List<OrderDto> orderDtos = new ArrayList<>();

        if (Objects.nonNull(dataObj)) {
            JSONArray orderList = dataObj.getJSONArray("list");
            if (Objects.nonNull(orderList) && orderList.size() > 0) {
                for (Integer i = 0; i < orderList.size(); i++) {
                    JSONObject orderObj = orderList.getJSONObject(i);

                    OrderDto orderDto = new OrderDto();

                    // 映射ASICS字段到标准OrderDto字段
                    orderDto.setOrderId(orderObj.getString("orderNumber"));
                    orderDto.setPlatCode(orderObj.getString("channelCode"));
                    orderDto.setShopCode(orderObj.getString("storeCode"));
                    orderDto.setOrderTime(orderObj.getString("orderTime"));
                    orderDto.setPayTime(orderObj.getString("paymentTime"));
                    orderDto.setFinishTime(orderObj.getString("tradeEndTime"));
                    orderDto.setPayment(orderObj.getDouble("orderTotalAmount"));
                    orderDto.setTotalQuantity(orderObj.getInteger("itemQuantity"));

                    // 会员信息
                    orderDto.setMemberId(orderObj.getString("memberCode"));

                    // 地址信息
                    String province = orderObj.getString("province");
                    String city = orderObj.getString("city");
                    String district = orderObj.getString("district");
                    String country = orderObj.getString("country");

                    StringBuilder addressBuilder = new StringBuilder();
                    if (StringUtils.isNotEmpty(country)) {
                        addressBuilder.append(country);
                    }
                    if (StringUtils.isNotEmpty(province)) {
                        addressBuilder.append(province);
                    }
                    if (StringUtils.isNotEmpty(city)) {
                        addressBuilder.append(city);
                    }
                    if (StringUtils.isNotEmpty(district)) {
                        addressBuilder.append(district);
                    }
                    orderDto.setReceiverAddress(addressBuilder.toString());
                    orderDto.setReceiverProvince(province);
                    orderDto.setReceiverCity(city);
                    orderDto.setReceiverDistrict(district);

                    // 保存原始数据作为扩展信息
                    orderDto.setProjectExt(JSON.toJSONString(orderObj));

                    orderDtos.add(orderDto);
                }
            }

            // 设置分页信息
            PageWrap<OrderDto> pageWrap = new PageWrap<>();
            pageWrap.setItems(orderDtos);
            pageWrap.setPage(dataObj.getInteger("pageNum"));
            pageWrap.setPageSize(dataObj.getInteger("pageSize"));
            pageWrap.setTotalCount(dataObj.getInteger("total"));

            restWrap.buildSuccess(pageWrap);
        } else {
            // 没有数据时返回空列表
            PageWrap<OrderDto> pageWrap = new PageWrap<>();
            pageWrap.setItems(orderDtos);
            restWrap.buildSuccess(pageWrap);
        }

        return JSON.toJSONString(restWrap);
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memOrder.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        // 构建ASICS格式的测试响应数据
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("errorCode", "");
        object.put("errorMessage", "");
        object.put("timeStamp", System.currentTimeMillis());

        // 构建data对象
        JSONObject dataObj = new JSONObject();
        dataObj.put("firstPage", true);
        dataObj.put("hasNextPage", false);
        dataObj.put("hasPreviousPage", false);
        dataObj.put("lastPage", true);
        dataObj.put("pageNum", 1);
        dataObj.put("pageSize", 20);
        dataObj.put("pages", 1);
        dataObj.put("size", 2);
        dataObj.put("total", 2);

        // 构建订单列表
        JSONArray orderList = new JSONArray();

        // 订单1
        JSONObject order1 = new JSONObject();
        order1.put("baNo", "BA001");
        order1.put("channelCode", "WECHAT");
        order1.put("channelId", 1);
        order1.put("city", "北京市");
        order1.put("country", "中国");
        order1.put("couponNo", "COUPON001");
        order1.put("district", "朝阳区");
        order1.put("itemQuantity", 2);
        order1.put("memberCode", "ASICS_MEMBER_001");
        order1.put("mobile", "18710426216");
        order1.put("orderNumber", "ORDER_001");
        order1.put("orderTime", "2024-09-17 16:28:38");
        order1.put("orderTotalAmount", 299.99);
        order1.put("paymentTime", "2024-09-17 16:30:15");
        order1.put("province", "北京市");
        order1.put("refundedAmount", 0.0);
        order1.put("storeCode", "STORE_001");
        order1.put("storeId", 1001);
        order1.put("thirdOrderNumber", "THIRD_001");
        order1.put("tradeEndTime", "2024-09-17 18:00:00");
        orderList.add(order1);

        // 订单2
        JSONObject order2 = new JSONObject();
        order2.put("baNo", "BA002");
        order2.put("channelCode", "APP");
        order2.put("channelId", 2);
        order2.put("city", "上海市");
        order2.put("country", "中国");
        order2.put("couponNo", "");
        order2.put("district", "浦东新区");
        order2.put("itemQuantity", 1);
        order2.put("memberCode", "ASICS_MEMBER_001");
        order2.put("mobile", "18710426216");
        order2.put("orderNumber", "ORDER_002");
        order2.put("orderTime", "2024-09-16 14:20:15");
        order2.put("orderTotalAmount", 199.99);
        order2.put("paymentTime", "2024-09-16 14:22:30");
        order2.put("province", "上海市");
        order2.put("refundedAmount", 50.0);
        order2.put("storeCode", "STORE_002");
        order2.put("storeId", 1002);
        order2.put("thirdOrderNumber", "THIRD_002");
        order2.put("tradeEndTime", "2024-09-16 16:00:00");
        orderList.add(order2);

        dataObj.put("list", orderList);
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        // 生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        // 测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

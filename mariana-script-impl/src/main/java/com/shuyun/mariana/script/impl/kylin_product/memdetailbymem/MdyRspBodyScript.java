package com.shuyun.mariana.script.impl.kylin_product.memdetailbymem;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseKylinProScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        JSONObject jsonObject = rspObj.getJSONObject("data");
        if (Objects.nonNull(jsonObject)) {
            memberDetailRsp.setMobile(jsonObject.getString("mobile"));
            memberDetailRsp.setMemberName(jsonObject.getString("fullName"));
            memberDetailRsp.setBirthday(jsonObject.getString("dateOfBirth"));
            memberDetailRsp.setGender(jsonObject.getString("gender"));
            memberDetailRsp.setHeadImgUrl(jsonObject.getString("headImgUrl"));
            memberDetailRsp.setMemberId(jsonObject.getString("memberId"));
            memberDetailRsp.setProjectExt(JSON.toJSONString(jsonObject));
        }

        RestWrap<MemberDetailRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDetailByMem.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"error_code\": null,\n" +
                "    \"msg\": \"success\",\n" +
                "    \"data\": {\n" +
                "        \"memberId\": \"WHOOF9UUFZ\",\n" +
                "        \"id\": \"WHOOF9UUFZ\",\n" +
                "        \"brand\": null,\n" +
                "        \"enrollChannel\": \"WECHAT\",\n" +
                "        \"enrollShopCode\": null,\n" +
                "        \"enrollShopName\": null,\n" +
                "        \"ascriptionShopCode\": null,\n" +
                "        \"ascriptionShopName\": null,\n" +
                "        \"ascriptionChannel\": null,\n" +
                "        \"ascriptionChannelName\": null,\n" +
                "        \"fullName\": \"Bella333\",\n" +
                "        \"gender\": \"F\",\n" +
                "        \"mobile\": \"18163912056\",\n" +
                "        \"email\": null,\n" +
                "        \"dateOfBirth\": \"1998-01-01\",\n" +
                "        \"marriage\": null,\n" +
                "        \"status\": \"ACTIVE\",\n" +
                "        \"optionalFieldData\": {\n" +
                "            \"manageBc\": \"sdfd\"\n" +
                "        },\n" +
                "        \"enrollTime\": \"2025-01-14T06:26:54.462Z\"\n" +
                "    }\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

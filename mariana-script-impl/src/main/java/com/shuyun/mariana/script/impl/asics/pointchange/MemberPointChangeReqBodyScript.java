package com.shuyun.mariana.script.impl.asics.pointchange;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分变更请求体转换脚本
 * 功能：将内部标准积分变更协议转换为ASICS系统所需的字段格式
 */
public class MemberPointChangeReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        JSONObject bodyParam = JSON.parseObject(reqBodyInJson);
        JSONObject asicsObject = new JSONObject();

        // 解析配置信息
        String appCfgJson = bodyParam.getString("appCfgJson");
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }

        String bizExtJson = bodyParam.getString("bizExtJson");
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 会员标识 - 优先使用memberId作为memberCode
        String memberId = bodyParam.getString("memberId");
        if (StringUtils.isNotEmpty(memberId)) {
            asicsObject.put("memberCode", memberId);
        } else if (bizExtObj != null && StringUtils.isNotEmpty(bizExtObj.getString("memberCode"))) {
            asicsObject.put("memberCode", bizExtObj.getString("memberCode"));
        }

        // 积分数量
        BigDecimal changePoint = bodyParam.getBigDecimal("changePoint");
        if (changePoint != null) {
            asicsObject.put("points", changePoint.intValue());
        }

        // 描述信息
        String desc = bodyParam.getString("desc");
        if (StringUtils.isNotEmpty(desc)) {
            asicsObject.put("summary", desc);
        }

        // 交易ID
        String sequence = bodyParam.getString("sequence");
        if (StringUtils.isNotEmpty(sequence)) {
            asicsObject.put("transactionId", sequence);
        }

        // 积分变更类型映射
        String pointChangeType = bodyParam.getString("pointChangeType");
        if (StringUtils.isNotEmpty(pointChangeType)) {
            // 映射到ASICS的sourceType和obtainType
            mapPointChangeType(asicsObject, pointChangeType, changePoint);
        }

        // 设置渠道信息和会员系统ID - 使用基类方法
        setChannel(asicsObject, appCfgObj, bodyParam);
        setMembershipSystemId(asicsObject, appCfgObj, bodyParam);

        String result = asicsObject.toJSONString();
        return result;
    }

    /**
     * 映射积分变更类型到ASICS字段
     */
    private void mapPointChangeType(JSONObject asicsObject, String pointChangeType, BigDecimal changePoint) {
        switch (pointChangeType.toUpperCase()) {
            case "POINT_GAIN":
            case "POINT_UNFREEZE":
                // 积分发放
                asicsObject.put("sourceType", 0); // 0, "系统入账" 1, "积分消费";
                asicsObject.put("obtainType", 0); // 0, "系统入账" 1, "积分消费";
                break;
            case "POINT_CONSUME":
            case "POINT_FREEZE":
//            case "POINT_FREEZE_CONSUME":
                // 积分消费
                asicsObject.put("sourceType", 1); // 0, "系统入账" 1, "积分消费";
                asicsObject.put("obtainType", 1); // 0, "系统入账" 1, "积分消费";
                // 积分消费时需要传递负数
                if (changePoint != null && changePoint.compareTo(BigDecimal.ZERO) > 0) {
                    asicsObject.put("points", -changePoint.intValue());
                } else if (changePoint != null) {
                    asicsObject.put("points", changePoint.intValue());
                }
                break;
//            case "POINT_REVERT":
//                // 返还消费积分
//                asicsObject.put("sourceType", 3); // 3-返还积分
//                asicsObject.put("obtainType", 3); // 3-退款返还
//                break;
//            case "POINT_FREEZE":
//                // 冻结积分
//                asicsObject.put("sourceType", 4); // 4-冻结积分
//                asicsObject.put("obtainType", 4); // 4-系统冻结
//                break;
//            case "POINT_UNFREEZE":
//                // 解冻积分
//                asicsObject.put("sourceType", 5); // 5-解冻积分
//                asicsObject.put("obtainType", 5); // 5-系统解冻
//                break;
//            case "POINT_FREEZE_CONSUME":
//                // 冻结消费
//                asicsObject.put("sourceType", 6); // 6-冻结消费
//                asicsObject.put("obtainType", 6); // 6-冻结扣减
//                break;
//            default:
//                // 默认为积分发放
//                asicsObject.put("sourceType", 1);
//                asicsObject.put("obtainType", 1);
//                break;
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("changePoint", 100);
        object.put("desc", "购物获得积分");
        object.put("sequence", "TXN_" + System.currentTimeMillis());
        object.put("pointChangeType", "POINT_GAIN");
        object.put("source", "PURCHASE");
        object.put("operator", "SYSTEM");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"memberCode\":\"ASICS_CODE_001\",\"businessType\":\"PURCHASE\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

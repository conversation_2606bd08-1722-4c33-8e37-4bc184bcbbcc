package com.shuyun.mariana.script.impl.huiyou.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.huiyou.BaseHuiyouScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * https://open.shuyun.com/#/apidoc?type=41&apiId=34
 */
public class MdyMemPointGainReqBodyScript extends BaseHuiyouScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        //反序列化为协议
        JSONObject reqBodyObj = JSON.parseObject(reqBodyInJson);

        JSONObject bizExt = JSON.parseObject(reqBodyObj.getString("bizExtJson"));
        reqBodyObj.put("userKey", bizExt.getString("userKey"));
        String sourceId = reqBodyObj.getString("sequence");
        if (StringUtils.isEmpty(sourceId)) {
            sourceId = UUID.randomUUID().toString();
        }
        reqBodyObj.put("sourceId", sourceId);
        reqBodyObj.put("point", reqBodyObj.getString("changePoint"));
        reqBodyObj.put("desc", reqBodyObj.getString("desc"));

        String result = reqBodyObj.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memberPointGain.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("sequence", "sequence");
        object.put("expired", "expired");
        object.put("created", "created");
        object.put("openId", "id");
        object.put("source", "source");
        object.put("changePoint", 90);
        object.put("operator", "operator");
        object.put("desc", "desc");
        object.put("pointChangeType", "POINT_CONSUME");

        JSONObject extObj = new JSONObject();
        extObj.put("userKey", "yfGdrqhzpmRQM6zf7Pnlmna9jFUYXCIqo3YzTsYvdPo5ZarwNlovVWdOQomrIQ00");
        object.put("bizExtJson", extObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

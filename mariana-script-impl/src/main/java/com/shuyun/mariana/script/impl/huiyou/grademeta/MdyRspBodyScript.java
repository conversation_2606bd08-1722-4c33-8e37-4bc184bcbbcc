package com.shuyun.mariana.script.impl.huiyou.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.huiyou.BaseHuiyouScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseHuiyouScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject dataObj = rspObj.getJSONObject("data");

        List<GradeMetaDto> resultJsonArray = new ArrayList<>();
        if (Objects.nonNull(dataObj)) {
            JSONArray gradeObjArray = dataObj.getJSONArray("dataList");
            for (int i = 0; i < gradeObjArray.size(); i++) {
                GradeMetaDto gradeMetaDto = new GradeMetaDto();

                JSONObject jsonObj = gradeObjArray.getJSONObject(i);
                gradeMetaDto.setGradeId(jsonObj.getString("levelId"));
                gradeMetaDto.setGradeName(jsonObj.getString("levelName"));
                gradeMetaDto.setGradeSort(jsonObj.getInteger("sort"));

                resultJsonArray.add(gradeMetaDto);
            }
        }

        RestWrap<List<GradeMetaDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultJsonArray);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memGradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String rspBody = "{\n" +
                "    \"success\": true,\n" +
                "    \"params\": {\n" +
                "        \"_biz\": \"shuyuncemla\"\n" +
                "    },\n" +
                "    \"code\": \"00000\",\n" +
                "    \"rid\": \"d9571f6745dd8374dac4d5c2f65bfd2dn00\",\n" +
                "    \"_biz\": \"shuyuncemla\",\n" +
                "    \"message\": \"操作成功\",\n" +
                "    \"data\": {\n" +
                "        \"dataList\": [\n" +
                "            {\n" +
                "                \"levelId\": \"8914566771771459239\",\n" +
                "                \"levelName\": \"普通会员\",\n" +
                "                \"sort\": 0\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"520039611948158650\",\n" +
                "                \"levelName\": \"注册会员\",\n" +
                "                \"sort\": 1\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"3057895867044611019\",\n" +
                "                \"levelName\": \"建档用户\",\n" +
                "                \"sort\": 2\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"1055393730192165684\",\n" +
                "                \"levelName\": \"金卡会员\",\n" +
                "                \"sort\": 3\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"1477091592660143690\",\n" +
                "                \"levelName\": \"铂金会员\",\n" +
                "                \"sort\": 4\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"4029505718032223118\",\n" +
                "                \"levelName\": \"钻石会员\",\n" +
                "                \"sort\": 5\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"178771559064292491\",\n" +
                "                \"levelName\": \"红运郎品牌顾问\",\n" +
                "                \"sort\": 6\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"4900236402890525288\",\n" +
                "                \"levelName\": \"意见领袖\",\n" +
                "                \"sort\": 7\n" +
                "            },\n" +
                "            {\n" +
                "                \"levelId\": \"1469885301726202139\",\n" +
                "                \"levelName\": \"黑金会员\",\n" +
                "                \"sort\": 8\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, rspBody);
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

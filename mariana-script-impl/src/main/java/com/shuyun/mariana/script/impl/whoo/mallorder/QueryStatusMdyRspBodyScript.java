package com.shuyun.mariana.script.impl.whoo.mallorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class QueryStatusMdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject orderRsp = new JSONObject();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            String status = dataObj.getString("transCD");
            /**
             * 20 - 领取
             --就是对应 订单完成

             30 - 取消
             --对应 订单取消或者关闭

             10 - 申请
             --对应初始下单状态
             */
            //转换状态
            /**
             * //ordered(已下单),completed(已完成),closed(已关闭)
             */
            String mappingStatus = status;
            if (status.equals("10")) {
                mappingStatus = "ordered";
            } else if (status.equals("20")) {
                mappingStatus = "completed";
            } else if (status.equals("30")) {
                mappingStatus = "closed";
            }

            orderRsp.put("status", mappingStatus);
        }

        RestWrap<JSONObject> restWrap = new RestWrap<>();
        restWrap.buildSuccess(orderRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "queryMallOrderStatus.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"code\":\"0\",\"message\":\"\",\"data\":{\"invoiceNumber\":\"20250220001\",\"courierCompany\":\"顺丰\",\"courierStatus\":\"SIGN\"}}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

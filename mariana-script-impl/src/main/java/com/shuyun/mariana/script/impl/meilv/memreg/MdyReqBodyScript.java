package com.shuyun.mariana.script.impl.meilv.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseMeiLvScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(memberRegReq));
        jsonObject.put("memberName", memberRegReq.getMemberName());
        jsonObject.put("nickname", memberRegReq.getNickName());
        jsonObject.put("appType", "WECHAT_MINI_PROGRAM");
        buildBirthday(memberRegReq.getBirthday(),jsonObject);
        buildMemberType(memberRegReq,jsonObject);
        WxAddressDto dftAddress = memberRegReq.getDftAddress();
        if (dftAddress!=null&&dftAddress.getProvinceName()!=null&&!"".equalsIgnoreCase(dftAddress.getProvinceName())){
            jsonObject.put("province", dftAddress.getProvinceName());
        }
        if (dftAddress!=null&&dftAddress.getCityName()!=null&&!"".equalsIgnoreCase(dftAddress.getCityName())){
            jsonObject.put("city", dftAddress.getCityName());
        }
        if (dftAddress!=null&&dftAddress.getCountyName()!=null&&!"".equalsIgnoreCase(dftAddress.getCountyName())){
            jsonObject.put("district", dftAddress.getCountyName());
        }
        JSONObject extObj = JSON.parseObject(memberRegReq.getBizExtJson());
        String gender=jsonObject.getString("gender");
        if (gender==null || "".equalsIgnoreCase(gender)){
            //反序列化扩展字段
            if (Objects.nonNull(extObj)) {
                String genderStr = extObj.getString("gender");
                jsonObject.put("gender", genderStr);
            }
        }
        if (Objects.nonNull(extObj)) {
            String shopCode = extObj.getString("shopcode");
            String guideCode = extObj.getString("guidecode");
            if (shopCode!=null&&!"".equalsIgnoreCase(shopCode)){
                jsonObject.put("shopCode",shopCode);
            }
            if (guideCode!=null&&!"".equalsIgnoreCase(guideCode)){
                jsonObject.put("salesrepId",guideCode);
            }
        }
        String shopCode = jsonObject.getString("shopCode");
        if (shopCode==null || "".equalsIgnoreCase(shopCode)){
            jsonObject.put("shopCode","SCRM002");
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

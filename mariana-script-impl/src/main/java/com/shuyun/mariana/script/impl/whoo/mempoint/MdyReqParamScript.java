package com.shuyun.mariana.script.impl.whoo.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseWhooScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        //反序列化为协议
        BaseMember baseMember = JSON.parseObject(reqBodyInJson, BaseMember.class);
        JSONObject inputParam = JSONObject.parseObject(reqBodyInJson);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();

        addParameters.put("overduePoint", "Y");
        addParameters.put("customerNo", baseMember.getAppId() + "_" + baseMember.getOpenId());

        String bizExtJson=inputParam.getString("bizExtJson");
        JSONObject extObj = JSON.parseObject(bizExtJson);

        String campaignStart = "2024-12-01 12:00:00";
        String campaignEnd = "2026-12-27 12:00:00";
        addParameters.put("campaignStart", campaignStart);
        addParameters.put("campaignEnd", campaignEnd);
        if (null != extObj) {
            campaignStart = extObj.getString("campaignStart");
            campaignEnd = extObj.getString("campaignEnd");

            if (StringUtils.isNotEmpty(campaignStart) && StringUtils.isNotEmpty(campaignEnd)) {
                addParameters.put("campaignStart", campaignStart);
                addParameters.put("campaignEnd", campaignEnd);
            }
        }

        //反序列化 ext_cfg，得到卡计划ID
        String appCfgJson = baseMember.getAppCfgJson();
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            addParameters.put("memberType", appCfgObj.getString("programCode"));
            addParameters.put("channelType", appCfgObj.getString("channel"));
        }
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memPoint.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("tenant", "tenant");
        object.put("unionId", "unionId");

        JSONObject jsonObject = new JSONObject();

        object.put("bizExtJson", jsonObject.toJSONString());

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

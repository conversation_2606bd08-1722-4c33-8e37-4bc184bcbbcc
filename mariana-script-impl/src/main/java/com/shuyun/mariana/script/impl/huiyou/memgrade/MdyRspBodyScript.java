package com.shuyun.mariana.script.impl.huiyou.memgrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memgrade.MemberGradeQueryRsp;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.huiyou.BaseHuiyouScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseHuiyouScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberGradeQueryRsp memberGradeQueryRsp = new MemberGradeQueryRsp();
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            memberGradeQueryRsp = JSON.parseObject(JSON.toJSONString(dataObj), MemberGradeQueryRsp.class);
            memberGradeQueryRsp.setGradeId(dataObj.getString("levelId"));
            memberGradeQueryRsp.setGradeName(dataObj.getString("levelName"));
        }

        RestWrap<MemberGradeQueryRsp> restWrap = new RestWrap<>();
        restWrap.buildSuccess(memberGradeQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memGrade.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("levelId", "DVF20240709WX00003");
        dataObj.put("levelName", "123");
        dataObj.put("gradeExpired", "2024-07-09 18:14:01");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

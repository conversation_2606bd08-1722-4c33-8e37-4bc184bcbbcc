package com.shuyun.mariana.script.impl.ccms.graderecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.graderecord.GradeRecordDto;
import com.shuyun.cem.std.member.protocol.graderecord.GradeRecordTypeEnum;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap pageWrap = new PageWrap();
        List<GradeRecordDto> gradeRecordDtos = new ArrayList<>();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONArray itemArrObj = dataObj.getJSONArray("list");
            pageWrap.setPageSize(dataObj.getInteger("pageSize"));
            pageWrap.setTotalCount(dataObj.getInteger("totals"));;
            pageWrap.setPage(dataObj.getInteger("pageNum"));
            if (Objects.nonNull(itemArrObj)) {
                for (int i = 0; i < itemArrObj.size(); i++) {

                    //组装流水
                    JSONObject gradeRecordObj = itemArrObj.getJSONObject(i);

                    GradeRecordDto gradeRecordDto = new GradeRecordDto();
                    gradeRecordDto.setMemberId(gradeRecordObj.getString("memberId"));

                    gradeRecordDto.setOriginalGradeId(gradeRecordObj.getString("sourceGrade"));
                    gradeRecordDto.setOriginalGradeName(gradeRecordObj.getString("bgradeName"));

                    gradeRecordDto.setCurrentGradeId(gradeRecordObj.getString("grade"));
                    gradeRecordDto.setCurrentGradeName(gradeRecordObj.getString("gradeName"));

                    String type = gradeRecordObj.getString("type");
                    String realType = null;
                    if ("KEEPING".equals(type)) {
                        realType = GradeRecordTypeEnum.HOLD_BACK_GRADE.name();
                    } else if ("UPGRADE".equals(type)) {
                        realType = GradeRecordTypeEnum.UPGRADE.name();
                    } else if ("DEGRADE".equals(type)) {
                        realType = GradeRecordTypeEnum.DEGRADE.name();
                    }
                    gradeRecordDto.setRecordType(realType);

                    gradeRecordDto.setChangeTime(gradeRecordObj.getString("changeTime"));

                    gradeRecordDtos.add(gradeRecordDto);
                }
            }
        }

        pageWrap.setItems(gradeRecordDtos);
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "gradeRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String recordRspStr = "{\n" +
                "    \"code\": 10000,\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"pageSize\": 10,\n" +
                "        \"totals\": 3,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"gradeName\": \"普通会员123\",\n" +
                "                \"bgradeName\": \"店铺客户\",\n" +
                "                \"created\": \"2024-08-14 15:29:33\",\n" +
                "                \"source\": \"SERVICE\",\n" +
                "                \"partnerSequence\": \"1723620573023984730\",\n" +
                "                \"type\": \"DEGRADE\",\n" +
                "                \"version\": \"1723620573023984730\",\n" +
                "                \"operator\": \"数云技术支持:meng.lv\",\n" +
                "                \"changeTime\": \"2024-08-14 15:29:33\",\n" +
                "                \"platCode\": \"DATAWINNER\",\n" +
                "                \"sequence\": \"1723620573023984730\",\n" +
                "                \"expired\": null,\n" +
                "                \"grade\": 1,\n" +
                "                \"shopId\": \"wxaada9391b766b633\",\n" +
                "                \"id\": \"123opmmb6YlYX2j54qtuGLUyCLvBVz03160\",\n" +
                "                \"sourceGrade\": 2,\n" +
                "                \"desc\": \"降级为店铺客户\",\n" +
                "                \"memberId\": 100865762652\n" +
                "            },\n" +
                "            {\n" +
                "                \"gradeName\": \"店铺客户\",\n" +
                "                \"bgradeName\": \"普通会员123\",\n" +
                "                \"created\": \"2024-08-14 15:05:28\",\n" +
                "                \"source\": \"SERVICE\",\n" +
                "                \"partnerSequence\": \"1723619128239041573\",\n" +
                "                \"type\": \"UPGRADE\",\n" +
                "                \"version\": \"1723619128239041573\",\n" +
                "                \"operator\": \"数云技术支持:meng.lv\",\n" +
                "                \"changeTime\": \"2024-08-14 15:05:28\",\n" +
                "                \"platCode\": \"DATAWINNER\",\n" +
                "                \"sequence\": \"1723619128239041573\",\n" +
                "                \"expired\": \"2025-08-01 23:59:59\",\n" +
                "                \"grade\": 2,\n" +
                "                \"shopId\": \"wxaada9391b766b633\",\n" +
                "                \"id\": \"123opmmb6YlYX2j54qtuGLUyCLvBVz03160\",\n" +
                "                \"sourceGrade\": 1,\n" +
                "                \"desc\": \"升级为普通会员123\",\n" +
                "                \"memberId\": 100865762652\n" +
                "            },\n" +
                "            {\n" +
                "                \"gradeName\": \"店铺客户\",\n" +
                "                \"bgradeName\": \"店铺客户\",\n" +
                "                \"created\": \"2024-08-14 09:39:53\",\n" +
                "                \"source\": \"SYSTEM\",\n" +
                "                \"partnerSequence\": \"1823534976470102091\",\n" +
                "                \"type\": \"KEEPING\",\n" +
                "                \"version\": \"1823534976470102091\",\n" +
                "                \"operator\": \"SYSTEM\",\n" +
                "                \"changeTime\": \"2024-08-14 09:39:53\",\n" +
                "                \"platCode\": \"DATAWINNER\",\n" +
                "                \"sequence\": \"1823534976470102091\",\n" +
                "                \"expired\": null,\n" +
                "                \"grade\": 1,\n" +
                "                \"shopId\": \"wxaada9391b766b633\",\n" +
                "                \"id\": \"123opmmb6YlYX2j54qtuGLUyCLvBVz03160\",\n" +
                "                \"sourceGrade\": 1,\n" +
                "                \"desc\": \"会员等级合并\",\n" +
                "                \"memberId\": 100865762652\n" +
                "            }\n" +
                "        ],\n" +
                "        \"httpCode\": true,\n" +
                "        \"pageNum\": 1\n" +
                "    }\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, recordRspStr);
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
//        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

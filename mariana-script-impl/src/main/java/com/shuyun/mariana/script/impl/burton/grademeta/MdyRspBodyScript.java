package com.shuyun.mariana.script.impl.burton.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        JSONArray items = JSON.parseArray(rspBodyInJson);
        ArrayList<GradeMetaDto> gradeMetaDtos = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            JSONObject jsonObject = items.getJSONObject(i);
            GradeMetaDto gradeMetaDto = new GradeMetaDto();
            gradeMetaDto.setGradeId(jsonObject.getString("id"));
            gradeMetaDto.setGradeName(jsonObject.getString("name"));
            gradeMetaDto.setGradeSort(jsonObject.getInteger("sort"));
            if ("单板滑手".equals(gradeMetaDto.getGradeName())){
                HashMap<String, String> map = new HashMap<>();
                map.put("desc","完成注册入会,即可成为单板滑手");
                gradeMetaDto.setProjectExt(JSON.toJSONString(map));
            }
            if ("绿道滑手".equals(gradeMetaDto.getGradeName())){
                HashMap<String, String> map = new HashMap<>();
                map.put("desc","任意直营渠道消费,即可成为绿道滑手");
                gradeMetaDto.setProjectExt(JSON.toJSONString(map));
            }
            if ("蓝道滑手".equals(gradeMetaDto.getGradeName())){
                HashMap<String, String> map = new HashMap<>();
                map.put("desc","两年直营渠道累计消费满10,000元,即可成为蓝道滑手");
                gradeMetaDto.setProjectExt(JSON.toJSONString(map));
            }
            if ("黑道滑手".equals(gradeMetaDto.getGradeName())){
                HashMap<String, String> map = new HashMap<>();
                map.put("desc","两年直营渠道累计消费满30,000元，即可成为黑道滑手");
                gradeMetaDto.setProjectExt(JSON.toJSONString(map));
            }
            gradeMetaDtos.add(gradeMetaDto);
        }
        RestWrap<List<GradeMetaDto>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(gradeMetaDtos);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[\n" +
                "    {\n" +
                "        \"id\": 60066,\n" +
                "        \"name\": \"单板滑手\",\n" +
                "        \"sort\": 1\n" +
                "    },\n" +
                "    {\n" +
                "        \"id\": 60067,\n" +
                "        \"name\": \"绿道滑手\",\n" +
                "        \"sort\": 2\n" +
                "    },\n" +
                "    {\n" +
                "        \"id\": 60068,\n" +
                "        \"name\": \"蓝道滑手\",\n" +
                "        \"sort\": 3\n" +
                "    },\n" +
                "    {\n" +
                "        \"id\": 60069,\n" +
                "        \"name\": \"黑道滑手\",\n" +
                "        \"sort\": 4\n" +
                "    }\n" +
                "]");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.meilv.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.memorder.MemberOrderQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseMeiLvScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberOrderQueryReq memberOrderQueryReq = JSON.parseObject(reqBodyInJson, MemberOrderQueryReq.class);
        memberOrderQueryReq.setFields(null);
        //反序列化为协议
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);
        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        addParameters.put("memberType", "samsonite");
        JSONObject appCfgJson = JSON.parseObject(memberOrderQueryReq.getAppCfgJson());
        if (Objects.nonNull(appCfgJson)) {
            String memberType = appCfgJson.getString("memberType");
            addParameters.put("memberType", memberType);
        }
        addParameters.put("currentPage", String.valueOf(memberOrderQueryReq.getPage()));
        addParameters.put("pageSize", String.valueOf(memberOrderQueryReq.getPageSize()));
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        String memberId=jsonObject.getString("memberId");
        JSONObject extObj = JSON.parseObject(memberOrderQueryReq.getBizExtJson());
        if (extObj!=null){
            if (memberId==null || "".equalsIgnoreCase(memberId)) {
                memberId = extObj.getString("memberId");
            }
            addParameters.put("memberId", memberId);
            String orderStatus = extObj.getString("orderStatus");
            if (orderStatus!=null&&!"".equalsIgnoreCase(orderStatus)){
                addParameters.put("status", String.valueOf(memberOrderQueryReq.getPageSize()));
            }
            String orderBeginTime = extObj.getString("orderBeginTime");
            if (orderBeginTime!=null&&!"".equalsIgnoreCase(orderBeginTime)){
                addParameters.put("orderBeginTime",orderBeginTime);
            }
            String orderEndTime = extObj.getString("orderEndTime");
            if (orderEndTime!=null&&!"".equalsIgnoreCase(orderEndTime)){
                addParameters.put("orderEndTime",orderEndTime);
            }

            String channelType = extObj.getString("channelType");
            if (channelType!=null&&!"".equalsIgnoreCase(channelType)){
                addParameters.put("channelType",channelType);
            }
        }
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);
        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memOrder.reqParam";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=36
 */
public class MdyReqParamScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.PUT.name());
        requestHandle.setRequestMethod(requestMethod);
        //反序列化为协议
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);
        JSONObject parseObject = JSON.parseObject(reqBodyInJson);
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(memberMdyReq));
        jsonObject.put("memberId", parseObject.getString("memberId"));
        jsonObject.put("appId", memberMdyReq.getAppId());
        jsonObject.put("appType", "WECHAT_MINI_PROGRAM");
        jsonObject.put("memberType", "BURTON");
        jsonObject.put("mobile", memberMdyReq.getMobile());
        jsonObject.put("openId", memberMdyReq.getOpenId());
        jsonObject.put("unionId", memberMdyReq.getUnionId());
        jsonObject.put("memberName", memberMdyReq.getMemberName());
        jsonObject.put("nickname", memberMdyReq.getNickName());
        if (StringUtils.isNotEmpty(memberMdyReq.getGender())) {
            jsonObject.put("gender", memberMdyReq.getGender());
        }else {
            jsonObject.put("gender", "O");
        }
        WxAddressDto dftAddress = memberMdyReq.getDftAddress();
        if (dftAddress!=null&&dftAddress.getProvinceName()!=null&&!"".equalsIgnoreCase(dftAddress.getProvinceName())){
            jsonObject.put("province", dftAddress.getProvinceName());
        }
        if (dftAddress!=null&&dftAddress.getCityName()!=null&&!"".equalsIgnoreCase(dftAddress.getCityName())){
            jsonObject.put("city", dftAddress.getCityName());
        }
        if (dftAddress!=null&&dftAddress.getCountyName()!=null&&!"".equalsIgnoreCase(dftAddress.getCountyName())){
            jsonObject.put("district", dftAddress.getCountyName());
        }
        JSONObject extObj = JSON.parseObject(memberMdyReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            JSONObject object = new JSONObject();
            String skiTime = extObj.getString("skiTime");
            if (StringUtils.isNotEmpty(skiTime)) {
                object.put("skiTime",skiTime);
            }
            if (Objects.nonNull(object)) {
                jsonObject.put("bindingExtProperties  ", object.toJSONString());
            }
        }
        //非必填
        if (Objects.nonNull(memberMdyReq.getHeadImgUrl())){
            jsonObject.put("headImgUrl",memberMdyReq.getHeadImgUrl());
        }
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        String result = jsonObject.toJSONString();
        requestParameter.setCustomizeParameter(result);
        requestHandle.setParameter(requestParameter);

        return JSON.toJSONString(requestHandle);
    }



    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memMdy.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        HashMap<String, Object> map = new HashMap<>();
        map.put("currGradeName","绿道滑手");
        object.put("bizExtJson", JSON.toJSONString(map));
        object.put("memberId", "50cfc6def6c2421ab27467075f3bec6b");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

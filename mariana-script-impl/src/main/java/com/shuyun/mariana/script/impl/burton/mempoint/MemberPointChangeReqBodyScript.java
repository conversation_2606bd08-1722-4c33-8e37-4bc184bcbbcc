package com.shuyun.mariana.script.impl.burton.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointGainReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

public class MemberPointChangeReqBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        MemberPointGainReq memberPointGainReq = JSON.parseObject(reqBodyInJson, MemberPointGainReq.class);
        //拼接公共参数
        JSONObject parseObject = JSON.parseObject(memberPointGainReq.getBizExtJson());
        jsonObject.put("channelType", "WECHAT");
        jsonObject.put("desc", memberPointGainReq.getDesc());
        jsonObject.put("memberId", jsonObject.getString("memberId"));
        jsonObject.put("memberType", "BURTON");
       jsonObject.put("point",memberPointGainReq.getChangePoint());
       jsonObject.put("shopCode",memberPointGainReq.getShopId());
        jsonObject.put("changeType", memberPointGainReq.getPointChangeType());
        jsonObject.put("pointAccountId","60064");
        jsonObject.put("uniqueId",jsonObject.getString("sequence"));
        jsonObject.put("tx",Boolean.FALSE);
        if (Objects.nonNull(parseObject)) {
            jsonObject.put("effectTime",parseObject.get("created"));
            jsonObject.put("expiredTime",parseObject.get("expired"));
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}
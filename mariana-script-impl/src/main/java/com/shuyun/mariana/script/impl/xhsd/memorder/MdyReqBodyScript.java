package com.shuyun.mariana.script.impl.xhsd.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memorder.MemberOrderQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseXhsdScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberOrderQueryReq memberOrderQueryReq = JSON.parseObject(req<PERSON>odyIn<PERSON><PERSON>, MemberOrderQueryReq.class);
        member<PERSON>rderQueryReq.setFields(null);
        //构建通用
        JSONObject jsonObject = buildCommon(memberOrderQueryReq);
        String orderType = jsonObject.getString("orderType");
        if ("1".equalsIgnoreCase(orderType)){
            jsonObject.remove("orderType");
        }else {
            if (orderType!=null&&!"".equalsIgnoreCase(orderType)){
                jsonObject.put("orderType", orderType);
            }
        }
        jsonObject.put("sortBy", "orderTime");
        jsonObject.put("sortType", "DESC");
        JSONObject extObj = JSON.parseObject(memberOrderQueryReq.getBizExtJson());
        if (extObj!=null){
            String channelType = extObj.getString("channelType");
            JSONArray jsonArray=new JSONArray();
            if ("offline".equalsIgnoreCase(channelType)){
                jsonArray.add("POS");
                jsonObject.put("channels", jsonArray);
            }
            if ("online".equalsIgnoreCase(channelType)){
                //渠道  POS-线下门店、WECHAT-微信、YSW-云书网、BXWHY-百姓文化云、HYZX-会员中心、SXHN-书香河南、 XHQD-新华青读、ZXDW-助学读物、
                jsonArray.add("WECHAT");
                jsonArray.add("YSW");
                jsonArray.add("BXWHY");
               // jsonArray.add("HYZX");
                jsonArray.add("SXHN");
                jsonArray.add("XHQD");
                jsonArray.add("ZXDW");
                jsonObject.put("channels", jsonArray);
            }
            String orderStatus = extObj.getString("orderStatus");
            if (orderStatus!=null&&!"".equalsIgnoreCase(orderStatus)){
                jsonObject.put("status", orderStatus);
            }
            String orderId = extObj.getString("orderId");
            if (orderId!=null&&!"".equalsIgnoreCase(orderId)){
                jsonObject.put("orderId",orderId);
            }
            String startTime = extObj.getString("startTime");
            if (startTime!=null&&!"".equalsIgnoreCase(startTime)){
                jsonObject.put("startTime",startTime);
            }
            String endTime = extObj.getString("endTime");
            if (endTime!=null&&!"".equalsIgnoreCase(endTime)){
                jsonObject.put("endTime",endTime);
            }
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memOrder.reqBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

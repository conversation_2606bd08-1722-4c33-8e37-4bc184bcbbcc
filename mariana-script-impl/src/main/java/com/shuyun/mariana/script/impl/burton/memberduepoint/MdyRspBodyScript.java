package com.shuyun.mariana.script.impl.burton.memberduepoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        Map<String,Object> projectExt=new HashMap<>();
        Map<String,Object> resultMap=new HashMap<>();
        resultMap.put("point",0);
        BigDecimal point = rspObj.getBigDecimal("point");
        if (point!=null){
            resultMap.put("point",point);
        }
        LocalDate today = LocalDate.now();
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        if (isAfterSeptemberFirst()){
            LocalDate sepFirst = LocalDate.of(today.getYear()+1, 9, 1);
            String format = df.format(sepFirst);
            projectExt.put("dueDate",format);
        }else {
            LocalDate sepFirst = LocalDate.of(today.getYear(), 9, 1);
            String format = df.format(sepFirst);
            projectExt.put("dueDate",format);
        }
        resultMap.put("projectExt",JSON.toJSONString(projectExt));
        RestWrap<Map<String,Object>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultMap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }
    public boolean isAfterSeptemberFirst() {
        LocalDate today = LocalDate.now();
        LocalDate sepFirst = LocalDate.of(today.getYear(), 9, 1);
        return today.isAfter(sepFirst);
    }
    @Override
    public String groovyFileName() {
        return projectDir()+ ".memberDuePointV2.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("point", "100");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

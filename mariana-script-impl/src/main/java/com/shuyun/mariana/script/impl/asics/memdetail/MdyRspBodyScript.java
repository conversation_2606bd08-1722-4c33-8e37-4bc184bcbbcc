package com.shuyun.mariana.script.impl.asics.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员详情查询响应体转换脚本 - 参考burton实现
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    public String cusRun(String reqBodyInJson, String rspBodyInJson) {
        /**
         * {
         *     "code": "2002",
         *     "errMsg": "不存在会员或者需要注册",
         *     "message": "不存在会员或者需要注册",
         *     "success": false
         * }
         */
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        Boolean success = rspObj.getBoolean("success");
        String code = rspObj.getString("code");
        if (!success && !"2002".equals(code)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return curDoRun(rspObj);
    }

    public String curDoRun(JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        
        // 处理ASICS系统的响应数据
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            buildMemRsp(memberDetailRsp, dataObj);
        }

        // 构建标准响应格式
        RestWrap<MemberDetailRsp> detailRspRestWrap = new RestWrap<>();
        detailRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(detailRspRestWrap);
        return result;
    }
    
    /**
     * 从多个渠道记录中选择合适的会员记录
     */
    private JSONObject selectMemberFromArray(String reqBodyInJson, JSONArray dataArray) {
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        String appId = inputParam.getString("appId");
        
        // 优先选择匹配appId且状态为ACTIVE的记录
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            String cardStatus = item.getString("cardStatus");
            String itemAppId = item.getString("appId");
            if ("ACTIVE".equals(cardStatus) && StringUtils.isNotEmpty(itemAppId) && itemAppId.equals(appId)) {
                return item;
            }
        }
        
        // 其次选择任何状态为ACTIVE的记录
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject item = dataArray.getJSONObject(i);
            String cardStatus = item.getString("cardStatus");
            if ("ACTIVE".equals(cardStatus)) {
                return item;
            }
        }
        
        // 如果没有活跃记录，返回第一个记录
        return dataArray.isEmpty() ? null : dataArray.getJSONObject(0);
    }
    
    /**
     * 构建会员响应对象 - 参考burton的简洁实现
     */
    private void buildMemRsp(MemberDetailRsp memberDetailRsp, JSONObject dataObj) {
        if (Objects.nonNull(dataObj)) {
            // 参考burton的方式：直接解析基础字段，然后进行特定字段映射
            
            // 保存原始响应数据作为扩展信息
            memberDetailRsp.setProjectExt(JSON.toJSONString(dataObj));
            
            // ASICS特有字段映射
            memberDetailRsp.setMemberId(dataObj.getString("memberCode"));
            memberDetailRsp.setMemberName(dataObj.getString("name"));
            memberDetailRsp.setNickname(dataObj.getString("nickName"));
            memberDetailRsp.setMobile(dataObj.getString("phone"));
//            memberDetailRsp.setUnionId(dataObj.getString("unionId"));
//            memberDetailRsp.setOpenId(dataObj.getString("wxShopOpenId"));
            memberDetailRsp.setHeadImgUrl(dataObj.getString("headImgUrl"));
            memberDetailRsp.setBirthday(dataObj.getString("birthday"));
            
            // 性别映射
            String sex = dataObj.getString("sex");
            if ("男".equals(sex)) {
                memberDetailRsp.setGender("M");
            } else if ("女".equals(sex)) {
                memberDetailRsp.setGender("F");
            } else {
                memberDetailRsp.setGender("U");
            }
            
            // 地址信息 - 参考burton的WxAddressDto方式
//            WxAddressDto wxAddressDto = new WxAddressDto();
//            wxAddressDto.setProvinceName(dataObj.getString("province"));
//            wxAddressDto.setCityName(dataObj.getString("city"));
//            wxAddressDto.setCountyName(dataObj.getString("county"));
//            wxAddressDto.setDetailInfo(dataObj.getString("address"));
//            memberDetailRsp.setDftAddress(wxAddressDto);
            
            // 积分和等级信息
            if (dataObj.containsKey("points")) {
                memberDetailRsp.setPoint(dataObj.getString("points"));
            }
//            if (dataObj.containsKey("totalPoints")) {
//                memberDetailRsp.setTotalPoint(dataObj.getDouble("totalPoints"));
//            }
            memberDetailRsp.setGradeId(dataObj.getString("levelId"));
            memberDetailRsp.setGradeName(dataObj.getString("level"));
//            memberDetailRsp.setNextGradeId(dataObj.getString("nextGrade"));
//            memberDetailRsp.setRegisterTime(dataObj.getString("registerDate"));
//            memberDetailRsp.setCardStatus(dataObj.getString("cardStatus"));
            
            // 渠道映射
//            String channel = dataObj.getString("channel");
//            if (StringUtils.isNotEmpty(channel)) {
//                memberDetailRsp.setChannel(mapChannelToName(channel));
//            }
        }
    }
    
    /**
     * 将渠道映射为名称
     */
    private String mapChannelToName(String channel) {
        if (StringUtils.isEmpty(channel)) {
            return "未知渠道";
        }
        
        try {
            int channelCode = Integer.parseInt(channel);
            switch (channelCode) {
                case 1:
                    return "天猫AT";
                case 2:
                    return "天猫AS";
                case 3:
                    return "天猫专卖店";
                case 4:
                    return "微信会员中心";
                case 5:
                    return "品牌小程序";
                case 15:
                    return "微信会员中心";
                default:
                    return "其他渠道(" + channel + ")";
            }
        } catch (NumberFormatException e) {
            return channel; // 如果不是数字，直接返回原值
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memDetail.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        
        // 请求参数
        param.put(ConstantKey.REQ_BODY_EXTEND, "{\n" +
                "    \"appId\": \"wxf492c06764b16035\",\n" +
                "    \"openId\": \"op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ\",\n" +
                "    \"unionId\": \"obm3r0ZqpCQ_Oe3zi7SwQltfzQKA\",\n" +
                "    \"mobile\": \"18710426216\",\n" +
                "    \"memberId\": \"ASICS_MEMBER_001\",\n" +
                "    \"bizExtJson\": \"{\\\"queryType\\\":\\\"memberId\\\"}\"\n" +
                "}");
        
        // 响应参数 - 参考burton的简洁格式
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"code\": \"200\",\n" +
                "    \"msg\": \"\",\n" +
                "    \"reqId\": null,\n" +
                "    \"data\": {\n" +
                "        \"id\": 343498403282946,\n" +
                "        \"wxUserId\": null,\n" +
                "        \"memberCode\": \"AS04699013\",\n" +
                "        \"name\": \"吕老大\",\n" +
                "        \"nickName\": null,\n" +
                "        \"headImgUrl\": \"http://asics-connext.oss-cn-zhangjiakou.aliyuncs.com/default.jpg\",\n" +
                "        \"tmallNickName\": null,\n" +
                "        \"qwStoreCode\": \"5499\",\n" +
                "        \"qwRegisterSa\": null,\n" +
                "        \"qwBelongStore\": null,\n" +
                "        \"qwBelongSa\": null,\n" +
                "        \"qwFirstAddDate\": null,\n" +
                "        \"qwCustomerId\": null,\n" +
                "        \"qwFriendAddDate\": null,\n" +
                "        \"level\": \"青铜会员\",\n" +
                "        \"cardStatus\": null,\n" +
                "        \"phone\": \"18710426217\",\n" +
                "        \"sex\": \"未知\",\n" +
                "        \"birthday\": null,\n" +
                "        \"email\": null,\n" +
                "        \"province\": null,\n" +
                "        \"city\": null,\n" +
                "        \"county\": null,\n" +
                "        \"address\": null,\n" +
                "        \"runningTag\": null,\n" +
                "        \"spotPreference\": null,\n" +
                "        \"weekDistance\": null,\n" +
                "        \"extraGears\": null,\n" +
                "        \"ownGears\": null,\n" +
                "        \"memberQr\": \"http://asics-connext.oss-cn-zhangjiakou.aliyuncs.com/asics-connext/memberQrBar/2025-07/QR-AS04699013.jpg\",\n" +
                "        \"height\": null,\n" +
                "        \"weight\": null,\n" +
                "        \"shoeSize\": null,\n" +
                "        \"archType\": null,\n" +
                "        \"testResult\": null,\n" +
                "        \"totalPoints\": 50,\n" +
                "        \"growths\": null,\n" +
                "        \"powers\": null,\n" +
                "        \"totalPowers\": null,\n" +
                "        \"infoPerfection\": null,\n" +
                "        \"infoUsedCrm\": null,\n" +
                "        \"isChangeBinding\": null,\n" +
                "        \"registerDate\": \"2025-07-09 18:00:02\",\n" +
                "        \"points\": 50,\n" +
                "        \"expireWarn\": \"2025-07-09:0\",\n" +
                "        \"atMark\": null,\n" +
                "        \"asMark\": null,\n" +
                "        \"unionId\": \"obm3r0eKtP-0uQsOwcRZbQIMHT9M4\",\n" +
                "        \"asOuid\": null,\n" +
                "        \"atOuid\": null,\n" +
                "        \"zmOuid\": null,\n" +
                "        \"omid\": null,\n" +
                "        \"memberBc\": \"http://asics-connext.oss-cn-zhangjiakou.aliyuncs.com/asics-connext/memberQrBar/2025-07/Bar-AS04699013.jpg\",\n" +
                "        \"shopperId\": null,\n" +
                "        \"wechatNickName\": \"ASICS CLUB 会员\",\n" +
                "        \"yzOpenId\": null,\n" +
                "        \"qwBind\": null,\n" +
                "        \"qwRegisterDate\": null,\n" +
                "        \"yzRegisterDate\": null,\n" +
                "        \"qwBindDate\": null,\n" +
                "        \"expire30DayPoint\": null,\n" +
                "        \"nextGrade\": null,\n" +
                "        \"consumeAmount\": null,\n" +
                "        \"storeId\": null,\n" +
                "        \"channel\": \"15\",\n" +
                "        \"wxRegisterDate\": \"2025-07-09 18:00:02\",\n" +
                "        \"tmark\": null,\n" +
                "        \"bopenId\": null,\n" +
                "        \"copenId\": null\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}");
        
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("cusRun", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

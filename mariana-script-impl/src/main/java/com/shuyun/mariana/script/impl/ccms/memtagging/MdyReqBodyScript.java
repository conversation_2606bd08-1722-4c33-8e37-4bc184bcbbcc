package com.shuyun.mariana.script.impl.ccms.memtagging;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.MemberTaggingReq;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.TaggingDto;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 *
 * https://open.shuyun.com/#/apidoc?type=23&apiId=88
 */
public class MdyReqBodyScript extends BaseCcmsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberTaggingReq memberTaggingReq = JSON.parseObject(reqBodyInJson, MemberTaggingReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberTaggingReq);

        //会员基础业务数据填充
        jsonObject.put("platAccount", memberTaggingReq.getOpenId());

        JSONArray tagsMark = new JSONArray();
        List<TaggingDto> tags = memberTaggingReq.getTags();
        for (TaggingDto taggingDto : tags) {
            JSONObject tagObj = new JSONObject();
            tagObj.put("tagId", taggingDto.getTagId());

            JSONArray tagValueArr = new JSONArray();
            List<String> tagValues = taggingDto.getTagValues();
            for (String tagValue : tagValues) {
                tagValueArr.add(tagValue);
            }
            tagObj.put("tagValue", tagValueArr);

            tagsMark.add(tagObj);
        }
        jsonObject.put("tagsMark", tagsMark);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memTagging.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String rsp = "{\n" +
                "  \"unionId\": \"opmmb6YlYX2j54qtuGLUyCLvBVz1\",\n" +
                "  \"platCode\": \"WEIXIN\",\n" +
                "  \"shopId\": \"wx8cfb12736db39346\",\n" +
                "  \"markScene\": \"OUTER_SYSTEM\",\n" +
                "  \"createTime\": \"2022-01-25 09:30:01\",\n" +
                "  \"tags\":[\n" +
                "    {\n" +
                "        \"tagId\":\"83066\",\n" +
                "        \"tagValues\":[\"a\"]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, rsp);
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

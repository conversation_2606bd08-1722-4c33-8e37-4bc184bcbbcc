package com.shuyun.mariana.script.impl.ccms.memduepoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.mempoint.MemberDuePointQueryRsp;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointQueryRsp;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberDuePointQueryRsp memberDuePointQueryRsp = new MemberDuePointQueryRsp();
        String duePoint = rspObj.getString("data");
        memberDuePointQueryRsp.setPoint(Integer.valueOf(duePoint));

        RestWrap<MemberDuePointQueryRsp> restWrap = new RestWrap<>();
        restWrap.buildSuccess(memberDuePointQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDuePoint.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        object.put("data", "456");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

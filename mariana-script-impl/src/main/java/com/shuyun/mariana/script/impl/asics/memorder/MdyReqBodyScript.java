package com.shuyun.mariana.script.impl.asics.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memorder.MemberOrderQueryReq;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员订单查询请求体转换脚本
 * 功能：将内部标准MemberOrderQueryReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        MemberOrderQueryReq memberOrderQueryReq = JSON.parseObject(reqBodyInJson, MemberOrderQueryReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);

        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();

        //解析配置信息
        String appCfgJson = memberOrderQueryReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }

        String bizExtJson = memberOrderQueryReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 会员标识 - 优先使用memberId作为memberCode
        String memberId = inputParam.getString("memberId");
        asicsObject.put("memberCode", memberId);

        // unionId
        if (StringUtils.isNotEmpty(memberOrderQueryReq.getUnionId())) {
            asicsObject.put("unionId", memberOrderQueryReq.getUnionId());
        }

        // 分页参数
        asicsObject.put("page", memberOrderQueryReq.getPage());
        asicsObject.put("size", memberOrderQueryReq.getPageSize());

        // 设置会员系统ID - 使用基类方法
        setMembershipSystemId(asicsObject, appCfgObj, inputParam);

        String result = asicsObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memOrder.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("page", 0);
        object.put("pageSize", 20);
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"memberCode\":\"ASICS_CODE_001\",\"orderStatus\":\"ALL\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

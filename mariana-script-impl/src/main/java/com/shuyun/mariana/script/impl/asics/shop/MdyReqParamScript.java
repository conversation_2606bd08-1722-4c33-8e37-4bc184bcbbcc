package com.shuyun.mariana.script.impl.asics.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //鍙嶅簭鍒楀寲璇锋眰body
        //鍙嶅簭鍒楀寲涓哄崗璁?
        BaseMember baseMember = JSON.parseObject(reqBodyInJson, BaseMember.class);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //澧炲姞鏌ヨ鍙傛暟
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();

        //鍙嶅簭鍒楀寲 ext_cfg锛屽緱鍒皃rogramCode
        String appCfgJson = baseMember.getAppCfgJson();
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            addParameters.put("programCode", appCfgObj.getString("programCode"));
        } else {
            addParameters.put("programCode", "asics");
        }
        addParameters.put("page", String.valueOf(baseMember.getPage()));
        addParameters.put("pageSize", String.valueOf(baseMember.getPageSize()));
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "shop.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("tenant", "tenant");
        object.put("unionId", "unionId");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //鐢熸垚鑴氭湰
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //娴嬭瘯鑴氭湰
        absScriptGenerate.testScript(scriptContext);
    }
}


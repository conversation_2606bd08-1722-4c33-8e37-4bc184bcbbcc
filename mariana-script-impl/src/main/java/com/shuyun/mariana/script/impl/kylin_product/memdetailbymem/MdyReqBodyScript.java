package com.shuyun.mariana.script.impl.kylin_product.memdetailbymem;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseKylinProScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);

        JSONObject jsonObject = buildCommon(memberDetailReq);

        String bizExtJson = memberDetailReq.getBizExtJson();
        String queryType = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            JSONObject bizJson = JSON.parseObject(bizExtJson);
            if (Objects.nonNull(bizJson)) {
                queryType = bizJson.getString("queryType");
            }
        }

        //memberId
        String memberId = inputParam.getString("memberId");
        if (StringUtils.isNotEmpty(memberId)) {
            jsonObject.put("memberId", memberId);
        } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile")) {
            JSONObject customizedQuery = new JSONObject();
            customizedQuery.put("mobile", memberDetailReq.getMobile());
            jsonObject.put("customizedQuery", customizedQuery);
        } else {
            //渠道信息
            jsonObject.put("customerNo", memberDetailReq.getAppId() + "_" + memberDetailReq.getOpenId());
        }

        //反序列化扩展字段
        JSONObject appCfgObj = JSON.parseObject(memberDetailReq.getAppCfgJson());
        if (Objects.nonNull(appCfgObj)) {
            String programCode = appCfgObj.getString("programCode");
            String channel = appCfgObj.getString("channel");

            jsonObject.put("programCode", programCode);
            jsonObject.put("enrollChannel", channel);

            //自定义查询字段
            JSONArray optJsonArr = appCfgObj.getJSONArray("optionalFields");
            if (Objects.nonNull(optJsonArr)) {
                List<String> optFieldArr = optJsonArr.toJavaList(String.class);
                jsonObject.put("optionalFields", optFieldArr);
            }
        }

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." +"memDetailByMem.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

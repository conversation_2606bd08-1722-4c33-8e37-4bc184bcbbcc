package com.shuyun.mariana.script.impl.kylin_product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.UUID;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 */
public abstract class BaseKylinProScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        String errorCode = rspObj.getString("error_code");
        if (StringUtils.isNotEmpty(errorCode)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("errorCode"));
            errRestWrap.setMessage(rspObj.getString("msg"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
        return jsonObject;
    }

    public void buildMemberBiz(JSONObject jsonObject, BaseMember baseMember) {
        JSONObject extObj = JSON.parseObject(baseMember.getAppCfgJson());
        if (Objects.nonNull(extObj)) {
            String programCode = extObj.getString("programCode");
            String channel = extObj.getString("channel");

            jsonObject.put("programCode", programCode);
            jsonObject.put("enrollChannel", channel);
        }
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "kylinProduct";
    }
}

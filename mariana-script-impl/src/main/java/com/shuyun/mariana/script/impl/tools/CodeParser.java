package com.shuyun.mariana.script.impl.tools;

import com.github.javaparser.Position;
import com.github.javaparser.StaticJavaParser;
import com.github.javaparser.ast.CompilationUnit;
import com.github.javaparser.ast.ImportDeclaration;
import com.github.javaparser.ast.NodeList;
import com.github.javaparser.ast.body.ClassOrInterfaceDeclaration;
import com.github.javaparser.ast.body.MethodDeclaration;
import com.github.javaparser.ast.expr.MethodCallExpr;
import com.github.javaparser.ast.expr.SimpleName;
import com.github.javaparser.ast.stmt.Statement;
import com.github.javaparser.ast.visitor.GenericVisitorAdapter;
import com.github.javaparser.ast.visitor.VoidVisitorAdapter;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/14
 * @Version 1.0
 **/
@Slf4j
public class CodeParser {
    public static void main(String[] args) throws FileNotFoundException {

    }

    public static ParseResultDto fetchScriptContent(String dir, Class clazz, Map<String, Boolean> includeFuncs, Set<String> excludeFuncs) {
        ParseResultDto parseResultDto = new ParseResultDto();

        Package packageName = clazz.getPackage();
        String baseDir = packageName.getName().replace(".", "\\");

        String clazzName = clazz.getSimpleName();

        String fileFullName = dir + "\\" + baseDir + "\\" + clazzName + ".java";
        File file = new File(fileFullName);
        CompilationUnit cu = null;
        try {
            cu = StaticJavaParser.parse(file);
        } catch (FileNotFoundException e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e.getMessage());
        }

        StringBuilder importStr = new StringBuilder("");

        //拼接import语句
        NodeList<ImportDeclaration> importDeclarations = cu.getImports();
        for (ImportDeclaration importDeclaration : importDeclarations) {
//            System.out.println(importDeclaration.toString());
            if (importDeclaration.toString().contains("com.shuyun.mariana.script.impl")) {
                continue;
            }
            importStr.append(importDeclaration.toString());
        }

        String className = clazzName.replace(".java", "");
        ClassOrInterfaceDeclaration classDeclaration = cu.getClassByName(className).orElse(null);

        List<MethodDeclaration> methodDeclarations = classDeclaration.getMethods();
//        if (CollectionUtils.isNotEmpty(includeFuncs)) {
//            methodDeclarations = methodDeclarations.stream().filter(item -> includeFuncs.contains(item.getName().getIdentifier())).collect(Collectors.toList());
//        }
        if (CollectionUtils.isNotEmpty(excludeFuncs)) {
            methodDeclarations = methodDeclarations.stream().filter(item -> !excludeFuncs.contains(item.getName().getIdentifier())).collect(Collectors.toList());
        }
        methodDeclarations = methodDeclarations.stream().filter(item -> !item.getAnnotationByClass(IgnoreGen.class).isPresent()).collect(Collectors.toList());

        StringBuilder funcStr = new StringBuilder("");
        for (MethodDeclaration methodDeclaration : methodDeclarations) {
            Boolean methodComplete = includeFuncs.get(methodDeclaration.getNameAsString());
            if (Objects.isNull(methodComplete)) {
                methodComplete = true;
            }
            if (methodComplete) {
                funcStr.append(methodDeclaration.toString().replace("@Override", ""));
                funcStr.append("\n");
            } else {
                NodeList<Statement> nodeList = methodDeclaration.getBody().get().getStatements();
                for (Statement st : nodeList) {
                    funcStr.append(st.toString());
                    funcStr.append("\n");
                }
            }
        }

        parseResultDto.setImportStr(importStr.toString());
        parseResultDto.setFuncStr(funcStr.toString());

        return parseResultDto;
    }

    private static class MethodNamePrinter extends VoidVisitorAdapter<Void> {

        @Override
        public void visit(MethodDeclaration methodDeclaration, Void arg) {


            Optional<Position> begin = methodDeclaration.getBegin();
            Position beginPosition = begin.get();
            int beginLine = beginPosition.line;
            Optional<Position> end = methodDeclaration.getEnd();
            Position endPosition = end.get();
            int endLine = endPosition.line;
            super.visit(methodDeclaration, arg);
            SimpleName simpleName = methodDeclaration.getName();
            String methodName = simpleName.getIdentifier();
//            TestNode node = new TestNode(methodName,beginLine, endLine);
//            System.out.println(node);
            System.out.println("MethodName:" + methodDeclaration.getName() + ":begin:" + beginLine + ":end:" + endLine);
        }
    }


    private static class FunctionCallVisitor extends GenericVisitorAdapter<Void, Void> {
        @Override
        public Void visit(MethodCallExpr n, Void arg) {
            System.out.println("function call: " + n.toString());
            String functionCall = n.toString();
            return super.visit(n, arg);
        }

        @Override
        public Void visit(MethodDeclaration n, Void arg) {
            System.out.println("function declaration: " + n.getNameAsString());
            return super.visit(n, arg);
        }
    }
}

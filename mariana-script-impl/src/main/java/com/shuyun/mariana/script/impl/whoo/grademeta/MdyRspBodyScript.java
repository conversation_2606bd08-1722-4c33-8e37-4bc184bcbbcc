package com.shuyun.mariana.script.impl.whoo.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<List<JSONObject>> restWrap = new RestWrap<>();

        JSONArray dataArr = rspObj.getJSONArray("data");
        List<JSONObject> metaList = new ArrayList<>();
        if (Objects.nonNull(dataArr) && dataArr.size() > 0) {
            for (Integer i = 0 ; i < dataArr.size(); i++) {
                JSONObject dataObj = dataArr.getJSONObject(i);

                JSONObject metaDto = new JSONObject();
                metaDto.put("gradeId", dataObj.getString("gradeId"));
                metaDto.put("gradeName", dataObj.getString("gradeName"));
                metaDto.put("effectDesc", dataObj.getString("effectDesc"));
                metaDto.put("holdBackPayment", dataObj.getDouble("holdBackPayment"));
                metaDto.put("currentGradePayment", dataObj.getDouble("currentGradePayment"));

                metaList.add(metaDto);
            }
        }

        restWrap.buildSuccess(metaList);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"code\": \"0\",\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"60006\",\n" +
                "            \"gradeId\": \"60006\",\n" +
                "            \"gradeName\": \"Tier 0\",\n" +
                "            \"nextGradeName\": \"Tier 1\",\n" +
                "            \"effectDesc\": \"永久有效\",\n" +
                "            \"holdBackPayment\": 0.0,\n" +
                "            \"currentGradePayment\": 0.0\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

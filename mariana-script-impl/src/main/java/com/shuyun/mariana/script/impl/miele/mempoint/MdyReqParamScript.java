package com.shuyun.mariana.script.impl.miele.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseMieleScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberPointQueryReq memberPointQueryReq = JSON.parseObject(reqBodyInJson, MemberPointQueryReq.class);
        //构建通用
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        JSONObject extObj = JSON.parseObject(memberPointQueryReq.getBizExtJson());
        String memberId=jsonObject.getString("memberId");
        if (memberId==null || "".equalsIgnoreCase(memberId)){
            if (Objects.nonNull(extObj)) {
                memberId = extObj.getString("memberId");
            }
        }
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        addParameters.put("programCode", "Miele");
        addParameters.put("channelType", "WECHAT");
        addParameters.put("memberId", memberId);
        JSONObject appCfgJson = JSON.parseObject(memberPointQueryReq.getAppCfgJson());
        if (Objects.nonNull(appCfgJson)) {
            String pointAccountId = appCfgJson.getString("pointAccountId");
            addParameters.put("pointAccountId", pointAccountId);
            if (pointAccountId!=null&&!"".equalsIgnoreCase(pointAccountId)){
                jsonObject.put("pointAccountId", Long.parseLong(pointAccountId));
            }
        }
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);
        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memPoint.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

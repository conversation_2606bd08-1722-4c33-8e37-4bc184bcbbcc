package com.shuyun.mariana.script.impl.kylin_product.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseKylinProScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONArray subArr = rspObj.getJSONArray("subjectRes");

        List<GradeMetaDto> resultJsonArray = new ArrayList<>();
        if (Objects.nonNull(subArr)) {
            JSONObject subObj =  subArr.getJSONObject(0);
            if (Objects.nonNull(subObj)) {
                JSONArray gradeHirArr = subObj.getJSONArray("gradeHierarchies");
                JSONObject hirObj = gradeHirArr.getJSONObject(0);
                JSONArray gradeDefArr =  hirObj.getJSONArray("gradeDefinitions");
                if (Objects.nonNull(gradeDefArr)) {
                    for (int i = 0; i < gradeDefArr.size(); i++) {
                        GradeMetaDto gradeMetaDto = new GradeMetaDto();

                        JSONObject jsonObj = gradeDefArr.getJSONObject(i);
                        gradeMetaDto.setGradeId(jsonObj.getString("id"));
                        gradeMetaDto.setGradeName(jsonObj.getString("name"));
                        gradeMetaDto.setGradeSort(jsonObj.getInteger("sort"));

                        resultJsonArray.add(gradeMetaDto);
                    }
                }
            }
        }

        RestWrap<List<GradeMetaDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultJsonArray);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");



        JSONObject dataObj = new JSONObject();

        JSONArray jsonArray = new JSONArray();
        JSONObject gradeObj = new JSONObject();
        gradeObj.put("gradeId", "20934");
        gradeObj.put("name", "test1");
        gradeObj.put("id", "1");

        jsonArray.add(gradeObj);

        dataObj.put("grades", jsonArray);
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"id\": 60001,\n" +
                "    \"name\": \"后忠诚度计划\",\n" +
                "    \"subjectRes\": [\n" +
                "        {\n" +
                "            \"id\": 60002,\n" +
                "            \"name\": \"后会员\",\n" +
                "            \"fqn\": \"data.mrm.member.whoo.Member\",\n" +
                "            \"gradeHierarchies\": [\n" +
                "                {\n" +
                "                    \"id\": 60005,\n" +
                "                    \"name\": \"后等级体系\",\n" +
                "                    \"gradeDefinitions\": [\n" +
                "                        {\n" +
                "                            \"id\": 60006,\n" +
                "                            \"name\": \"粉丝\",\n" +
                "                            \"sort\": 1\n" +
                "                        }\n" +
                "                    ]\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);
        JSONObject parseObject = JSON.parseObject(reqBodyInJson);
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(memberMdyReq));
        jsonObject.put("memberId", parseObject.getString("memberId"));
        jsonObject.put("appId", memberMdyReq.getAppId());
        jsonObject.put("appType", "WECHAT_MINI_PROGRAM");
        jsonObject.put("memberType", "BURTON");
        jsonObject.put("mobile", memberMdyReq.getMobile());
        jsonObject.put("openId", memberMdyReq.getOpenId());
        jsonObject.put("unionId", memberMdyReq.getUnionId());
        jsonObject.put("memberName", memberMdyReq.getMemberName());
        jsonObject.put("nickname", memberMdyReq.getNickName());
        if (StringUtils.isNotEmpty(memberMdyReq.getGender())) {
            jsonObject.put("gender", memberMdyReq.getGender());
        }else {
            jsonObject.put("gender", "O");
        }
        if (StringUtils.isNotEmpty(memberMdyReq.getBirthday())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate parsed = LocalDate.parse(memberMdyReq.getBirthday(), formatter);
            jsonObject.put("birthYear", parsed.getYear());
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MM-dd");
            jsonObject.put("birthDay",dateTimeFormatter.format(parsed) );
        }
        WxAddressDto dftAddress = memberMdyReq.getDftAddress();
        if (dftAddress!=null&&dftAddress.getProvinceName()!=null&&!"".equalsIgnoreCase(dftAddress.getProvinceName())){
            jsonObject.put("provinceName", dftAddress.getProvinceName());
        }
        if (dftAddress!=null&&dftAddress.getCityName()!=null&&!"".equalsIgnoreCase(dftAddress.getCityName())){
            jsonObject.put("cityName", dftAddress.getCityName());
        }
        if (dftAddress!=null&&dftAddress.getCountyName()!=null&&!"".equalsIgnoreCase(dftAddress.getCountyName())){
            jsonObject.put("districtName", dftAddress.getCountyName());
        }
        JSONObject extObj = JSON.parseObject(memberMdyReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            JSONObject object = new JSONObject();
            String skiTime = extObj.getString("skiTime");
            if (StringUtils.isNotEmpty(skiTime)) {
                object.put("skiTime",skiTime);
            }
            if (Objects.nonNull(object)) {
                jsonObject.put("customizedProperties", object);
            }
        }
        //非必填
        if (Objects.nonNull(memberMdyReq.getHeadImgUrl())){
            jsonObject.put("headImgUrl",memberMdyReq.getHeadImgUrl());
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memMdy.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

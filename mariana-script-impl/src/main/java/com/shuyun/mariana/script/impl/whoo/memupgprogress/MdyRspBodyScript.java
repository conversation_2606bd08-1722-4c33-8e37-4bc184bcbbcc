package com.shuyun.mariana.script.impl.whoo.memupgprogress;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject memberUpgProgressRsp = new JSONObject();
        memberUpgProgressRsp.put("upConsumeMoney", rspObj.getDouble("upgradeAmount"));

        Double upgradeAmount = rspObj.getDouble("upgradeAmount");
        if (Objects.isNull(upgradeAmount)) {
            upgradeAmount = 0.0;
        }
        Double holdBackAmount = rspObj.getDouble("holdBackAmount");
        if (Objects.isNull(holdBackAmount)) {
            holdBackAmount = 0.0;
        }
        Double amount = rspObj.getDouble("amount");

        Double toUpgradeAmount = 0D;
        //最高等级不返回升级门槛
        if (Objects.nonNull(upgradeAmount) && upgradeAmount.compareTo(amount) > 0) {
            toUpgradeAmount = upgradeAmount - amount;
        }

        //最低等级时 不返回保级门槛
        Double toHoldBackAmount = 0D;
        Double holdBackAmountSum = rspObj.getDouble("holdBackAmountSum");
        if (Objects.nonNull(holdBackAmount) && Objects.nonNull(holdBackAmountSum) && holdBackAmount.compareTo(holdBackAmountSum) > 0) {
            toHoldBackAmount = holdBackAmount - holdBackAmountSum;
        }

        toUpgradeAmount = Math.round(toUpgradeAmount * 100.0)/100.0;
        if (Double.compare(toUpgradeAmount, 0.0) == 0 || Double.compare(toUpgradeAmount, 0) == 0) {
            toUpgradeAmount = 0.01;
        }
        rspObj.put("toUpgradeAmount", toUpgradeAmount);
        toHoldBackAmount = Math.round(toHoldBackAmount * 100.0)/100.0;
        rspObj.put("toHoldBackAmount", toHoldBackAmount);

        rspObj.put("upgradeAmount", upgradeAmount);
        rspObj.put("holdBackAmount", holdBackAmount);

        memberUpgProgressRsp.put("projectExt", rspObj.toJSONString());
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(memberUpgProgressRsp);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "upgprogress.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String recordRspStr = "{\n" +
                "    \"id\": 60050,\n" +
                "    \"name\": \"铂金卡\",\n" +
                "    \"effectiveTime\": \"2025-03-24T03:19:04.544Z\",\n" +
                "    \"expiredTime\": \"2025-03-28T15:59:59.999Z\",\n" +
                "    \"amount\": 2280.53,\n" +
                "    \"upgradeAmount\": 8000.0,\n" +
                "    \"nextGradeName\": \"Tier 4\",\n" +
                "    \"holdBackAmount\": 4000.0,\n" +
                "    \"holdBackAmountSum\": 0\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, recordRspStr);
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
//        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.ccms.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject dataObj = rspObj.getJSONObject("data");

        PageWrap pageWrap = new PageWrap();
        List<ShopDto> resultJsonArray = new ArrayList<>();
        if (Objects.nonNull(dataObj)) {
            pageWrap.setPage(dataObj.getInteger("currentPage"));
            pageWrap.setPageSize(dataObj.getInteger("pageSize"));
            pageWrap.setTotalCount(dataObj.getInteger("total"));
            JSONArray shopArray = dataObj.getJSONArray("list");
            for (int i = 0; i < shopArray.size(); i++) {
                ShopDto shopDto = new ShopDto();

                JSONObject jsonObj = shopArray.getJSONObject(i);
                shopDto.setShopCode(jsonObj.getString("shopId"));
                shopDto.setShopName(jsonObj.getString("shopName"));
                shopDto.setShopTypeCode(jsonObj.getString("platCode"));

                resultJsonArray.add(shopDto);
            }
        }

        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "shop.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String mock = "{\n" +
                "  \"code\": 10000,\n" +
                "  \"success\": true,\n" +
                "  \"data\": {\n" +
                "    \"currentPage\": 1,\n" +
                "    \"pageSize\": 2,\n" +
                "    \"list\": [\n" +
                "      {\n" +
                "        \"platCode\": \"OFFLINE\",\n" +
                "        \"partner\": \"qiakr\",\n" +
                "        \"shopId\": \"201809201454\",\n" +
                "        \"uniShopId\": \"OFFLINE|201809201454\",\n" +
                "        \"partnerShopId\":\"22444\",\n" +
                "        \"shopName\": \"大青青的店\",\n" +
                "        \"country\": \"\",\n" +
                "        \"state\": \"NORMAL\",\n" +
                "        \"city\": \"杭州市\",\n" +
                "        \"district\": \"江干区\",\n" +
                "        \"town\": \"\",\n" +
                "        \"address\": \"\",\n" +
                "        \"openDate\": \"\",\n" +
                "        \"created\": \"2018-11-09 10:44:16\",\n" +
                "        \"modified\": \"2018-11-09 10:44:16\"\n" +
                "      },\n" +
                "      {\n" +
                "        \"platCode\": \"OFFLINE\",\n" +
                "        \"partner\": \"\",\n" +
                "        \"shopId\": \"5423355767465b69\",\n" +
                "        \"uniShopId\": \"OFFLINE|5423355767465b69\",\n" +
                "        \"partnerShopId\":\"22444\",\n" +
                "        \"shopName\": \"xxxx\",\n" +
                "        \"country\": \"\",\n" +
                "        \"state\": \"\",\n" +
                "        \"city\": \"\",\n" +
                "        \"district\": \"\",\n" +
                "        \"town\": \"\",\n" +
                "        \"address\": \"\",\n" +
                "        \"openDate\": \"2018-06-22\",\n" +
                "        \"created\": \"2018-06-22 11:24:23\",\n" +
                "        \"modified\": \"2018-09-13 12:34:08\"\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, mock);
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

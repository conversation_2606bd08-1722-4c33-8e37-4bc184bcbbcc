package com.shuyun.mariana.script.impl.whoo.goods;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        List<JSONObject> resultJsonArray = new ArrayList<>();
        PageWrap pageWrap = new PageWrap();
        JSONArray dataObj = rspObj.getJSONArray("data");
        if (Objects.nonNull(dataObj)) {
            for (int i = 0; i < dataObj.size(); i++) {
                JSONObject goodsDto = new JSONObject();
                JSONObject jsonObj = dataObj.getJSONObject(i);
                goodsDto.put("productId", jsonObj.getString("productCode"));
                goodsDto.put("productName", jsonObj.getString("productName"));
                goodsDto.put("price", jsonObj.getDouble("retailPrice"));
                goodsDto.put("itemType", jsonObj.getString("itemType"));
                //是否有效Y:有效 N:无效
                goodsDto.put("status", "Y".equals(jsonObj.getString("isValid")) ? 1 : 0);
                goodsDto.put("channelType", jsonObj.getString("channelType"));


                goodsDto.put("projectExt", jsonObj);

                resultJsonArray.add(goodsDto);
            }
        }
        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".goodsQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"code\": \"0\",\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"COMBINE0001000006\",\n" +
                "            \"bundleId\": \"COMBINE0001000006\",\n" +
                "            \"bundleName\": \"套餐D\",\n" +
                "            \"price\": 2.0,\n" +
                "            \"itemType\": \"1\",\n" +
                "            \"disabledFlag\": false,\n" +
                "            \"channelType\": \"OMS\",\n" +
                "            \"createTime\": \"2021-06-03T06:24:28.000Z\",\n" +
                "            \"updateTime\": \"2022-05-12T08:03:18.000Z\",\n" +
                "            \"lastSync\": \"2025-01-16T08:00:26.010Z\"\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

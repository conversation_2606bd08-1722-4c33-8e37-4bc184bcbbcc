package com.shuyun.mariana.script.impl.whoo.mallorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class QueryExpressMdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject expressQueryRsp = new JSONObject();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            expressQueryRsp.put("logisticsNo", dataObj.getString("invoiceNumber"));
            expressQueryRsp.put("companyName", dataObj.getString("courierCompany"));
            expressQueryRsp.put("logisticsStatus", dataObj.getString("courierStatus"));

            expressQueryRsp.put("mobile", dataObj.getString("vendorMobileNo"));
            expressQueryRsp.put("receiverName", dataObj.getString("vendorName"));
            expressQueryRsp.put("province", dataObj.getString("vendorState"));
            expressQueryRsp.put("city", dataObj.getString("vendorCity"));
            expressQueryRsp.put("district", dataObj.getString("vendorDistrict"));
            expressQueryRsp.put("address", dataObj.getString("vendorAddr"));
        }

        RestWrap<JSONObject> restWrap = new RestWrap<>();
        restWrap.buildSuccess(expressQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "queryMallOrderExpress.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"code\":\"0\",\"message\":\"\",\"data\":{\"invoiceNumber\":\"20250220001\",\"courierCompany\":\"顺丰\",\"courierStatus\":\"SIGN\"}}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.whoo.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseWhooScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberRegReq);

        //反序列化 ext_cfg，得到卡计划ID
        String appCfgJson = memberRegReq.getAppCfgJson();
        JSONObject appCfgObj = JSON.parseObject(appCfgJson);
        jsonObject.put("programCode", appCfgObj.getString("programCode"));
        jsonObject.put("brand", appCfgObj.getString("programCode"));
        jsonObject.put("enrollChannel", appCfgObj.getString("channel"));

        //渠道信息
        jsonObject.put("customerNo", memberRegReq.getAppId() + "_" + memberRegReq.getOpenId());
        jsonObject.put("unionId", memberRegReq.getUnionId());
        jsonObject.put("appId", memberRegReq.getAppId());
        jsonObject.put("openId", memberRegReq.getOpenId());
        jsonObject.put("fullName", memberRegReq.getMemberName());
        jsonObject.put("headImgUrl", memberRegReq.getHeadImgUrl());
        jsonObject.put("nick", memberRegReq.getNickName());
        jsonObject.put("gender", memberRegReq.getGender());
        jsonObject.put("mobile", memberRegReq.getMobile());
        jsonObject.put("dateOfBirth", memberRegReq.getBirthday());

        //默认门店设置为小程序
        jsonObject.put("enrollShopCode", memberRegReq.getAppId());
        jsonObject.put("enrollShopName", "微信会员中心");

        //extras
        String bizExtJson = memberRegReq.getBizExtJson();
        if (StringUtils.isNotEmpty(bizExtJson)) {
            JSONObject bizExtObj = JSON.parseObject(bizExtJson);
            //店铺ID和名字。如果传了 使用客户得
            if (StringUtils.isNotEmpty(bizExtObj.getString("enrollShopCode"))) {
                jsonObject.put("enrollShopCode", bizExtObj.getString("enrollShopCode"));
                jsonObject.put("enrollShopName", "");
            }
            //导购，如果传了 使用客户得
            if (StringUtils.isNotEmpty(bizExtObj.getString("enrollGuide"))) {
                jsonObject.put("enrollGuide", bizExtObj.getString("enrollGuide"));
            }
            //推荐人信息
            if (StringUtils.isNotEmpty(bizExtObj.getString("recommender"))) {
                jsonObject.put("recommender", bizExtObj.getString("recommender"));
            }

            JSONObject extras = new JSONObject();
            extras.put("registerSource", "微信会员中心");
            JSONArray optJsonArr = appCfgObj.getJSONArray("optionalFields");
            if (Objects.nonNull(optJsonArr)) {
                List<String> optFieldArr = optJsonArr.toJavaList(String.class);
                for (String optField : optFieldArr) {
                    if (StringUtils.isNotEmpty(bizExtObj.getString(optField))) {
                        extras.put(optField, bizExtObj.getString(optField));
                    }
                }
            }
            jsonObject.put("extras", extras);
        }

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("appCfgJson","{\"gender\":\"M\",\"registerGuide\":\"A99999\",\"registerGuideName\":\"数云业务部-丽丽\",\"registerShopCode\":\"SHOP002\",\"registerShopName\":\"SHOP002\"}");
        object.put("bizExtJson","{\"gender\":\"M\",\"registerGuide\":\"A99999\",\"registerGuideName\":\"数云业务部-丽丽\",\"registerShopCode\":\"SHOP002\",\"registerShopName\":\"SHOP002\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

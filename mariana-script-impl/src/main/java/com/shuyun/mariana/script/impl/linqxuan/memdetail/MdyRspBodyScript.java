package com.shuyun.mariana.script.impl.linqxuan.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=40
 */
public class MdyRspBodyScript extends BaseLinQXuanScriptBase {

    public String cusRun(String reqBodyInJson, String rspBodyInJson) {
        //success	string	接口返回代码, s: 正常; e: 错误; f: 失败
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        String success = rspObj.getString("success");
        if (!"s".equals(success)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return curDoRun(reqBodyInJson, rspObj);
    }

    public String curDoRun(String reqBodyInJson, JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            //reqBodyInJson
            MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);

            //基于查询方式 queryType：openId,mobile
            JSONObject extObj = JSON.parseObject(memberDetailReq.getBizExtJson());
            if (Objects.nonNull(extObj)) {
                String queryType = extObj.getString("queryType");
                //为空 则默认使用vipCode查询
                if (StringUtils.isEmpty(queryType) || (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile"))) {
                    memberDetailRsp.setMobile(dataObj.getString("tel"));
                    memberDetailRsp.setMemberId(dataObj.getString("vipCode"));
                    memberDetailRsp.setMemberName(dataObj.getString("cName"));
                    memberDetailRsp.setNickname(dataObj.getString("nickName"));
                    memberDetailRsp.setHeadImgUrl(dataObj.getString("headImg"));
                    memberDetailRsp.setGender(dataObj.getString("sex"));
                    memberDetailRsp.setBirthday(dataObj.getString("birthday"));

                    memberDetailRsp.setPoint(dataObj.getString("point"));
                    memberDetailRsp.setGradeId(dataObj.getString("vipLevelCode"));
                    memberDetailRsp.setGradeName(dataObj.getString("vipLevel"));

                    memberDetailRsp.setProjectExt(dataObj.toJSONString());
                } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("realOpenId")) {
                    JSONArray authArr = dataObj.getJSONArray("authorize");
                    if (Objects.nonNull(authArr)) {
                        for (int i = 0; i < authArr.size(); i++) {
                            if ("KT007".equals(authArr.getJSONObject(i).getString("type"))) {
                                String appId = authArr.getJSONObject(i).getString("appId");
                                String openId = authArr.getJSONObject(i).getString("key");

                                if (StringUtils.isNotEmpty(appId) && memberDetailReq.getAppId().equals(appId)
                                        && StringUtils.isNotEmpty(openId) && memberDetailReq.getOpenId().equals(openId)) {
                                    memberDetailRsp.setMobile(dataObj.getString("tel"));
                                    memberDetailRsp.setMemberId(dataObj.getString("vipCode"));
                                    memberDetailRsp.setMemberName(dataObj.getString("cName"));
                                    memberDetailRsp.setNickname(dataObj.getString("nickName"));
                                    memberDetailRsp.setHeadImgUrl(dataObj.getString("headImg"));
                                    memberDetailRsp.setGender(dataObj.getString("sex"));
                                    memberDetailRsp.setBirthday(dataObj.getString("birthday"));

                                    memberDetailRsp.setPoint(dataObj.getString("point"));
                                    memberDetailRsp.setGradeId(dataObj.getString("vipLevelCode"));
                                    memberDetailRsp.setGradeName(dataObj.getString("vipLevel"));

                                    memberDetailRsp.setProjectExt(dataObj.toJSONString());
                                }
                            }
                        }
                    }
                }
            } else {
                memberDetailRsp.setMobile(dataObj.getString("tel"));
                memberDetailRsp.setMemberId(dataObj.getString("vipCode"));
                memberDetailRsp.setMemberName(dataObj.getString("cName"));
                memberDetailRsp.setNickname(dataObj.getString("nickName"));
                memberDetailRsp.setHeadImgUrl(dataObj.getString("headImg"));
                memberDetailRsp.setGender(dataObj.getString("sex"));
                memberDetailRsp.setBirthday(dataObj.getString("birthday"));

                memberDetailRsp.setPoint(dataObj.getString("point"));
                memberDetailRsp.setGradeId(dataObj.getString("vipLevelCode"));
                memberDetailRsp.setGradeName(dataObj.getString("vipLevel"));

                memberDetailRsp.setProjectExt(dataObj.toJSONString());
            }
        }

        RestWrap<MemberDetailRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDetail.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("status", "REGISTERED");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("cusRun", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

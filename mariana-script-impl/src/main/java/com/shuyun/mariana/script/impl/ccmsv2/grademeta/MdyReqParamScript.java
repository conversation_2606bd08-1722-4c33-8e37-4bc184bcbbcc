package com.shuyun.mariana.script.impl.ccmsv2.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.memgrade.MemberGradeQueryReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.ccmsv2.BaseCcmsV2ScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseCcmsV2ScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberGradeQueryReq memberGradeQueryReq = JSON.parseObject(reqBodyInJson, MemberGradeQueryReq.class);
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);
        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        String appCfgJson = memberGradeQueryReq.getAppCfgJson();
        JSONObject appCfgObj = new JSONObject();
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        String cardPlanId = appCfgObj.getString("cardPlanId");
        if (StringUtils.isNotEmpty(memberGradeQueryReq.getCardPlanId())) {
            cardPlanId = memberGradeQueryReq.getCardPlanId();
        }
        addParameters.put("cardPlanId", cardPlanId);
        String tenant = appCfgObj.getString("tenant");
        if (StringUtils.isNotEmpty(memberGradeQueryReq.getTenant())) {
            tenant = memberGradeQueryReq.getTenant();
        }
        addParameters.put("tenant", tenant);
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);
        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".grademeta.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("dunhill", "WECHAT");
        object.put("cardPlanId", "BURTON");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

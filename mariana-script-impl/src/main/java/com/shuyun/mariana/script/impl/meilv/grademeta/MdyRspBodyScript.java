package com.shuyun.mariana.script.impl.meilv.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMeiLvScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        List<JSONObject> resultJsonArray = new ArrayList<>();
        JSONArray items = JSON.parseArray(rspBodyInJson);
        if (items!=null){
            for (int i = 0; i < items.size(); i++) {
                JSONObject jsonObj = items.getJSONObject(i);
                JSONObject jsonObjData=new JSONObject();
                String id = jsonObj.getString("id");
                String name=jsonObj.getString("name");
                if ("60005".equalsIgnoreCase(id)){
                    name="启航旅行家";
                }
                if ("60006".equalsIgnoreCase(id)){
                    name="先锋旅行家";
                }
                if ("60007".equalsIgnoreCase(id)){
                    name="达人旅行家";
                }
                if ("60008".equalsIgnoreCase(id)){
                    name="明星旅行家";
                }
                jsonObjData.put("gradeId",id);
                jsonObjData.put("gradeName",name);
                jsonObjData.put("gradeSort",jsonObj.getString("sort"));
                JSONObject projectExt =new JSONObject();
                projectExt.put("name",jsonObj.getString("name"));
                jsonObjData.put("projectExt",JSON.toJSONString(projectExt));
                resultJsonArray.add(jsonObjData);
            }
        }else {
            items=new JSONArray();
        }
        RestWrap<List<JSONObject>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(resultJsonArray);
        String result = JSON.toJSONString(restWrap);
        return result;
    }
    @Override
    public String groovyFileName() {
        return projectDir()+".gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[{\"name\":\"普卡会员\",\"id\":60005,\"sort\":1},{\"name\":\"银卡会员\",\"id\":60006,\"sort\":2},{\"name\":\"金卡会员\",\"id\":60007,\"sort\":3},{\"name\":\"铂金卡会员\",\"id\":60008,\"sort\":4}]");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.whoo.guide;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<PageWrap<JSONObject>> restWrap = new RestWrap<>();

        JSONArray dataArr = rspObj.getJSONArray("data");
        List<JSONObject> guideDtos = new ArrayList<>();
        if (Objects.nonNull(dataArr) && dataArr.size() > 0) {
            for (Integer i = 0 ; i < dataArr.size(); i++) {
                JSONObject guideObj = dataArr.getJSONObject(i);
                //如果没有二维码，过滤掉
                String qrCodeUrl = guideObj.getString("qrCodeUrl");
                if (StringUtils.isEmpty(qrCodeUrl)) {
                    continue;
                }

                //过滤掉离职的员工
                String loginYn = guideObj.getString("loginYn");
                if (StringUtils.isNotEmpty(loginYn) && loginYn.equals("N")) {
                    continue;
                }

                String prtnrEmpFl = guideObj.getString("prtnrEmpFl");
                if ("1106001".equals(prtnrEmpFl)) {
                    prtnrEmpFl = "店长";
                } else if ("1106002".equals(prtnrEmpFl)) {
                    prtnrEmpFl = "美容顾问";
                } else if ("1106003".equals(prtnrEmpFl)) {
                    prtnrEmpFl = "美容师";
                } else {
                    prtnrEmpFl = "美容顾问";
                }
                guideObj.put("prtnrEmpFl", prtnrEmpFl);

                guideDtos.add(guideObj);
            }
        }

        PageWrap<JSONObject> pageWrap = new PageWrap<>();
        pageWrap.setItems(guideDtos);
        restWrap.buildSuccess(pageWrap);

        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + ".guideQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"code\": \"0\",\n" +
                "    \"message\": \"success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"40741\",\n" +
                "            \"empId\": \"40741\",\n" +
                "            \"salOrgCD\": \"10\",\n" +
                "            \"prtnrId\": \"0000001792\",\n" +
                "            \"name\": \"赵琪\",\n" +
                "            \"entryDate\": \"2024-12-01\",\n" +
                "            \"mobileNo\": \"18660509716\",\n" +
                "            \"userId\": \"40741\",\n" +
                "            \"regEmpID\": \"01284\",\n" +
                "            \"regTmzoneCD\": \"GP0800\",\n" +
                "            \"regDate\": \"2024-12-01T00:33:57.000Z\",\n" +
                "            \"lastSync\": \"2025-01-05T07:58:43.190Z\",\n" +
                "            \"sex\": \"F\",\n" +
                "            \"prtnrTp\": \"1105002\",\n" +
                "            \"prtnrEmpFl\": \"1106002\",\n" +
                "            \"workStatCD\": \"1108001\",\n" +
                "            \"memberId\": \"40741\"\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

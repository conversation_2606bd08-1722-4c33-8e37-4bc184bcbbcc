package com.shuyun.mariana.script.impl.asics.tagmeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memtag.tagmeta.kylin.TagMetaDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=40
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject resultObj = JSON.parseObject(rspBodyInJson);
        String responseCode = resultObj.getString("responseCode");
        if (StringUtils.isEmpty(responseCode) || !"000000".equals(responseCode)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(resultObj.getString("responseCode"));
            errRestWrap.setMessage(resultObj.getString("responseMsg"));
            return JSON.toJSONString(errRestWrap);
        }

        List<TagMetaDto> tagMetaDtos = new ArrayList<>();

        JSONObject dataObj = resultObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONArray tagObjArr = dataObj.getJSONArray("content");
            if (Objects.nonNull(tagObjArr)) {
                for (Integer i = 0 ; i < tagObjArr.size(); i++) {
                    JSONObject tagObj = tagObjArr.getJSONObject(i);

                    //璺宠繃涓嬬嚎寰楁爣绛撅紝鏉ヨ嚜kylin tangming
                    String status = tagObj.getString("status");
                    if ("OFFLINE".equals(status)) {
                        continue;
                    }

                    TagMetaDto tagMetaDto = new TagMetaDto();
                    tagMetaDto.setCategoryId(tagObj.getLong("categoryId"));
                    tagMetaDto.setId(tagObj.getString("id"));
                    tagMetaDto.setName(tagObj.getString("name"));
                    tagMetaDto.setType(tagObj.getString("type"));
                    tagMetaDto.setTagValueType(tagObj.getString("tagValueType"));
                    tagMetaDto.setCharValues(JSON.parseArray(tagObj.getString("charValues"), String.class));

                    //杩囨护鎺塩harValues涓虹┖寰?鏉ヨ嚜鍚撮煩
                    List<String> charValues = tagMetaDto.getCharValues();
                    if (CollectionUtils.isEmpty(charValues)) {
                        continue;
                    }

                    tagMetaDtos.add(tagMetaDto);
                }
            }
        }

        RestWrap<List<TagMetaDto>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(tagMetaDtos);


        return JSON.toJSONString(restWrap);
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "tagMeta.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        String rspBodyInJson = "";


        JSONObject resultObj = JSON.parseObject(rspBodyInJson);
        System.out.println(resultObj);

        AbsScriptGenerate absScriptGenerate = buildInstance();
        //鐢熸垚鑴氭湰
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //娴嬭瘯鑴氭湰
        absScriptGenerate.testScript(scriptContext);
    }
}


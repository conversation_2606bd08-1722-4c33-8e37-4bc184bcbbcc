package com.shuyun.mariana.script.impl.whoo.tagmeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memtag.tagmeta.kylin.TagMetaDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=40
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject resultObj = JSON.parseObject(rspBodyInJson);
        String responseCode = resultObj.getString("responseCode");
        if (StringUtils.isEmpty(responseCode) || !"000000".equals(responseCode)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(resultObj.getString("responseCode"));
            errRestWrap.setMessage(resultObj.getString("responseMsg"));
            return JSON.toJSONString(errRestWrap);
        }

        List<TagMetaDto> tagMetaDtos = new ArrayList<>();

        JSONObject dataObj = resultObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONArray tagObjArr = dataObj.getJSONArray("content");
            if (Objects.nonNull(tagObjArr)) {
                for (Integer i = 0 ; i < tagObjArr.size(); i++) {
                    JSONObject tagObj = tagObjArr.getJSONObject(i);

                    //跳过下线得标签，来自kylin tangming
                    String status = tagObj.getString("status");
                    if ("OFFLINE".equals(status)) {
                        continue;
                    }

                    TagMetaDto tagMetaDto = new TagMetaDto();
                    tagMetaDto.setCategoryId(tagObj.getLong("categoryId"));
                    tagMetaDto.setId(tagObj.getString("id"));
                    tagMetaDto.setName(tagObj.getString("name"));
                    tagMetaDto.setType(tagObj.getString("type"));
                    tagMetaDto.setTagValueType(tagObj.getString("tagValueType"));
                    tagMetaDto.setCharValues(JSON.parseArray(tagObj.getString("charValues"), String.class));

                    //过滤掉charValues为空得 来自吴韩
                    List<String> charValues = tagMetaDto.getCharValues();
                    if (CollectionUtils.isEmpty(charValues)) {
                        continue;
                    }

                    tagMetaDtos.add(tagMetaDto);
                }
            }
        }

        RestWrap<List<TagMetaDto>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(tagMetaDtos);


        return JSON.toJSONString(restWrap);
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "tagMeta.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"responseCode\":\"000000\",\"responseMsg\":\"successful\",\"data\":{\"totalElement\":1,\"pageNo\":0,\"pageSize\":10,\"content\":[{\"id\":\"20241208153514649026496960000001\",\"name\":\"性别\",\"origin\":\"FRONT\",\"type\":\"MANUAL\",\"tagValueType\":\"HAVE_MULTI_ENUM_VALUE\",\"customerCount\":2,\"updatePolicy\":\"MANUAL_TAG\",\"status\":\"CALCULATE_SUCCESSFUL\",\"categoryId\":1,\"categoryPath\":\"/1\",\"manualTagFqn\":\"data.cdp.domain.manual.tag.model4\",\"charValues\":[\"男\",\"女\",\"未知\"]}]}}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        String rspBodyInJson = "{\"responseCode\":\"000000\",\"responseMsg\":\"successful\",\"data\":{\"totalElement\":1,\"pageNo\":0,\"pageSize\":10,\"content\":[{\"id\":\"20241208153514649026496960000001\",\"name\":\"性别\",\"origin\":\"FRONT\",\"type\":\"MANUAL\",\"tagValueType\":\"HAVE_MULTI_ENUM_VALUE\",\"customerCount\":2,\"updatePolicy\":\"MANUAL_TAG\",\"status\":\"CALCULATE_SUCCESSFUL\",\"categoryId\":1,\"categoryPath\":\"/1\",\"manualTagFqn\":\"data.cdp.domain.manual.tag.model4\",\"charValues\":[\"男\",\"女\",\"未知\"]}]}}";


        JSONObject resultObj = JSON.parseObject(rspBodyInJson);
        System.out.println(resultObj);

        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

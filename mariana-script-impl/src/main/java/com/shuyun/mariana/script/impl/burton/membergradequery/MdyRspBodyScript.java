package com.shuyun.mariana.script.impl.burton.membergradequery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        Map<String, Object> map = new HashMap<>();
        if (Objects.nonNull(rspObj)) {
            JSONObject customizedProperties = rspObj.getJSONObject("customizedProperties");
            map.put("gradeName",customizedProperties.getString("currGradeName"));
            map.put("gradeId",customizedProperties.getString("currGradeId"));
            map.put("memberId",rspObj.getString("memberId"));
        }
        RestWrap<Map> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(map);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memGradeQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"memberType\": \"BURTON\",\n" +
                "    \"gender\": \"O\",\n" +
                "    \"shopCode\": \"MembershipWechatApp\",\n" +
                "    \"shopName\": \"会员中心小程序\",\n" +
                "    \"customizedProperties\": {\n" +
                "        \"currGradeName\": \"单板滑手\",\n" +
                "        \"isCoach\": false,\n" +
                "        \"isCustomer\": true,\n" +
                "        \"type\": \"单板滑手\",\n" +
                "        \"ifTmInformation\": false,\n" +
                "        \"ifFans\": false\n" +
                "    },\n" +
                "    \"mobile\": \"18234048168\",\n" +
                "    \"cardNo\": \"4333d53b8c9f46acb07bf4a6f5865cbe\",\n" +
                "    \"memberId\": \"9e49d6c00eba41719d5898cb00b757a7\",\n" +
                "    \"firstRegisterChannelType\": \"WECHAT\",\n" +
                "    \"registerTime\": \"2024-06-26T05:51:09.722+08:00\"\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

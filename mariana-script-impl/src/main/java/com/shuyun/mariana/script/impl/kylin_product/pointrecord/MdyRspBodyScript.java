package com.shuyun.mariana.script.impl.kylin_product.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memgrade.MemberGradeQueryRsp;
import com.shuyun.cem.std.member.protocol.pointrecord.MemPointRecordQueryRsp;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseKylinProScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        PageWrap pageWrap = new PageWrap();
        List<PointRecordDto> pointRecordDtos = new ArrayList<>();

        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        JSONArray pointRecordArr = rspObj.getJSONArray("content");
        if (Objects.nonNull(pointRecordArr) && pointRecordArr.size() > 0) {
            for (Integer i = 0 ; i < pointRecordArr.size(); i++) {
                JSONObject pointRecObj = pointRecordArr.getJSONObject(i);

                PointRecordDto pointRecordDto = new PointRecordDto();
                pointRecordDto.setDescription(pointRecObj.getString("desc"));
                pointRecordDto.setPoint(pointRecObj.getDouble("point"));
                pointRecordDto.setSignedPoint(pointRecObj.getDouble("signedPoint"));

                Date date = pointRecObj.getDate("created");
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDateStr = dateFormat.format(date);
                pointRecordDto.setChangeTime(formattedDateStr);
                pointRecordDtos.add(pointRecordDto);
            }
        }

        pageWrap.setItems(pointRecordDtos);
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + ".pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("recordType", "SEND");
        itemObj.put("point", 500.0);

        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);

        JSONObject pageObj = new JSONObject();
        pageObj.put("page", 0);
        pageObj.put("pageSize", 20);
        pageObj.put("totalCount", 2);
        pageObj.put("items", itemArr);

        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        object.put("data", pageObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"content\": [\n" +
                "  \n" +
                "        {\n" +
                "            \"id\": \"cf602a2dfbbc495daeca584225c27af8\",\n" +
                "            \"planId\": 60001,\n" +
                "            \"planName\": \"后忠诚度计划\",\n" +
                "            \"subjectId\": 60002,\n" +
                "            \"subjectName\": \"后会员\",\n" +
                "            \"subjectFqn\": \"data.mrm.member.whoo.Member\",\n" +
                "            \"pointAccountTypeId\": 60037,\n" +
                "            \"pointAccountTypeName\": \"后积分账户\",\n" +
                "            \"memberId\": \"WHOOCWPFHZ\",\n" +
                "            \"point\": 2,\n" +
                "            \"recordType\": \"DEDUCT\",\n" +
                "            \"desc\": \"兑换抽奖次数\",\n" +
                "            \"totalPoint\": 118450,\n" +
                "            \"created\": \"2025-03-25T09:34:58.385+08:00\",\n" +
                "            \"changeMode\": \"INTERFACE\",\n" +
                "            \"traceId\": \"1904346233627914240\",\n" +
                "            \"key\": \"OPEN_UNFREEZE-1904346233627914240\",\n" +
                "            \"channelType\": \"WECHAT\",\n" +
                "            \"status\": \"USED\",\n" +
                "            \"signedPoint\": -2\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"387d9f1cab8d4b00929658434f3a29b9\",\n" +
                "            \"planId\": 60001,\n" +
                "            \"planName\": \"后忠诚度计划\",\n" +
                "            \"subjectId\": 60002,\n" +
                "            \"subjectName\": \"后会员\",\n" +
                "            \"subjectFqn\": \"data.mrm.member.whoo.Member\",\n" +
                "            \"pointAccountTypeId\": 60037,\n" +
                "            \"pointAccountTypeName\": \"后积分账户\",\n" +
                "            \"memberId\": \"WHOOCWPFHZ\",\n" +
                "            \"point\": 220,\n" +
                "            \"recordType\": \"DEDUCT\",\n" +
                "            \"desc\": \"积分商城兑礼消费\",\n" +
                "            \"totalPoint\": 118452,\n" +
                "            \"created\": \"2025-03-24T18:24:42.875+08:00\",\n" +
                "            \"changeMode\": \"INTERFACE\",\n" +
                "            \"traceId\": \"1904117159764598784\",\n" +
                "            \"key\": \"1904117159764598784\",\n" +
                "            \"channelType\": \"WECHAT\",\n" +
                "            \"status\": \"USED\",\n" +
                "            \"signedPoint\": -220\n" +
                "        },\n" +
                "       \n" +
                "      \n" +
                "       \n" +
                "        {\n" +
                "            \"id\": \"b754f6fbaeb4463bb5b3a2005cfe0e49\",\n" +
                "            \"planId\": 60001,\n" +
                "            \"planName\": \"后忠诚度计划\",\n" +
                "            \"subjectId\": 60002,\n" +
                "            \"subjectName\": \"后会员\",\n" +
                "            \"subjectFqn\": \"data.mrm.member.whoo.Member\",\n" +
                "            \"pointAccountTypeId\": 60037,\n" +
                "            \"pointAccountTypeName\": \"后积分账户\",\n" +
                "            \"memberId\": \"WHOOCWPFHZ\",\n" +
                "            \"point\": 2,\n" +
                "            \"recordType\": \"DEDUCT\",\n" +
                "            \"desc\": \"兑换抽奖次数\",\n" +
                "            \"totalPoint\": 118500,\n" +
                "            \"created\": \"2025-03-24T11:43:52.050+08:00\",\n" +
                "            \"changeMode\": \"INTERFACE\",\n" +
                "            \"traceId\": \"1904016284165517312\",\n" +
                "            \"key\": \"OPEN_UNFREEZE-1904016284165517312\",\n" +
                "            \"channelType\": \"WECHAT\",\n" +
                "            \"status\": \"USED\",\n" +
                "            \"signedPoint\": -2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"pageable\": {\n" +
                "        \"sort\": {\n" +
                "            \"unsorted\": true,\n" +
                "            \"sorted\": false,\n" +
                "            \"empty\": true\n" +
                "        },\n" +
                "        \"pageSize\": 20,\n" +
                "        \"offset\": 0,\n" +
                "        \"pageNumber\": 0,\n" +
                "        \"paged\": true,\n" +
                "        \"unpaged\": false\n" +
                "    },\n" +
                "    \"totalPages\": 27,\n" +
                "    \"totalElements\": 525,\n" +
                "    \"last\": false,\n" +
                "    \"numberOfElements\": 20,\n" +
                "    \"first\": true,\n" +
                "    \"size\": 20,\n" +
                "    \"sort\": {\n" +
                "        \"unsorted\": true,\n" +
                "        \"sorted\": false,\n" +
                "        \"empty\": true\n" +
                "    },\n" +
                "    \"number\": 0,\n" +
                "    \"empty\": false\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

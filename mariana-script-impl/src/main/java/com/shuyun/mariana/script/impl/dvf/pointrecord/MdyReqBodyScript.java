package com.shuyun.mariana.script.impl.dvf.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.pointrecord.MemPointRecordQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemPointRecordQueryReq pointRecordQueryReq = JSON.parseObject(reqBodyInJson, MemPointRecordQueryReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(pointRecordQueryReq);

        JSONObject extObj = JSON.parseObject(pointRecordQueryReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String pointRecordQueryType = extObj.getString("pointRecordQueryType");
            ////积分变更类型 SEND: 立即发放, DELAY_SEND: 延迟发放, EXPIRE: 过期, FREEZE: 冻结, UNFREEZE: 取消冻结,
            // DEDUCT: 扣减, ABOLISH: 作废, TIMER: 定时, RECALCULATE: 废弃重算,
            // SPECIAL_DEDUCT: 特殊扣除, SPECIAL_FREEZE: 特殊冻结, SPECIAL_UNFREEZE: 特殊解冻,
            // SPECIAL_ABOLISH: 特殊废弃, MANUAL_ABOLISH: 手动废弃, OPEN_FREEZE: 接口冻结, OPEN_UNFREEZE: 接口解冻
            if ("send".equalsIgnoreCase(pointRecordQueryType)){
                JSONArray jsonArray=new JSONArray();
                jsonArray.add("SEND");
                jsonArray.add("REVERSE_SEND");
                jsonArray.add("TIMER");
                jsonArray.add("OPEN_UNFREEZE");
                jsonArray.add("UNFREEZE");
                jsonArray.add("SPECIAL_UNFREEZE");
                jsonObject.put("recordTypes",jsonArray);
            }
            if ("deduct".equalsIgnoreCase(pointRecordQueryType)){
                JSONArray jsonArray=new JSONArray();
                jsonArray.add("DEDUCT");
                jsonArray.add("REVERSE_DEDUCT");
                jsonArray.add("OPEN_FREEZE");
                jsonArray.add("FREEZE");
                jsonArray.add("SPECIAL_DEDUCT");
                jsonArray.add("SPECIAL_FREEZE");
                jsonArray.add("EXPIRE");
                jsonArray.add("ABOLISH");
                jsonArray.add("RECALCULATE");
                jsonArray.add("SPECIAL_ABOLISH");
                jsonArray.add("MANUAL_ABOLISH");
                jsonObject.put("recordTypes",jsonArray);
            }
        }
        if (jsonObject.get("status")==null){
            //状态 DELAY:待生效, DELAY_FROZEN:待生效已冻结, DELAY_ABOLISH:待生效已作废, EXPIRE:已生效已过期, VALID:已生效,
            // FROZEN:已生效已冻结, USED:已生效已使用, ABOLISH:已生效已作废, FROZEN_ABOLISH:已生效已冻结已作废
            JSONArray jsonArray=new JSONArray();
            jsonArray.add("EXPIRE");
            jsonArray.add("VALID");
            jsonArray.add("FROZEN");
            jsonArray.add("USED");
            jsonArray.add("ABOLISH");
            jsonArray.add("FROZEN_ABOLISH");
            jsonObject.put("status",jsonArray);
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.pointRecord.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

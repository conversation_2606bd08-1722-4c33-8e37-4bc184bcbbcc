package com.shuyun.mariana.script.impl.asics.tagmeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.tagmeta.TagMetaQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS企微类目标签查询请求参数转换脚本
 * 功能：获取企微类目的标签配置
 */
public class MdyReqParamScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());

        requestHandle.setRequestMethod(requestMethod);

        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        JSONObject inputBody = JSON.parseObject(reqBodyInJson);

        Map<String, String> addParameters = new HashMap<>();
        addParameters.put("pageNo", inputBody.getString("page"));
        addParameters.put("pageSize", "1000");

        JSONArray cateArr = inputBody.getJSONArray("categoryIds");
        if (Objects.nonNull(cateArr)) {
            addParameters.put("categoryIds", cateArr.getString(0));
        } else {
            //鏉ヨ嚜kylin crm瀹氬埗
            addParameters.put("categoryIds", "1");
        }

        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "tagMeta.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberName", "test1");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //鐢熸垚鑴氭湰
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //娴嬭瘯鑴氭湰
        absScriptGenerate.testScript(scriptContext);
    }
}


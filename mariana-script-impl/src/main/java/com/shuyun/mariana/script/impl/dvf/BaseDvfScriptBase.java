package com.shuyun.mariana.script.impl.dvf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.UUID;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 */
public abstract class BaseDvfScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        Boolean success = rspObj.getBoolean("success");
        if (!success) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
        String appCfgJson = baseMember.getAppCfgJson();
        if (appCfgJson!=null&&!"".equalsIgnoreCase(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            jsonObject.put("bizCode", appCfgObj.getString("bizCode"));
        } else {
            jsonObject.put("bizCode", "DVF");
        }
        //拼接公共参数
        jsonObject.put("requestChannel", "WECHAT");
        jsonObject.put("requestSystem", "MEMBER_CENTER");
        jsonObject.put("transactionId", UUID.randomUUID().toString());
        jsonObject.put("scene", "mini_program");
        if (baseMember.getOpenId()!=null){
            JSONObject identityObj = new JSONObject();
            identityObj.put("userId", baseMember.getAppId() + "_" + baseMember.getOpenId());
            jsonObject.put("identify", identityObj);
        }
        return jsonObject;
    }

    public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
        jsonObject.put("fullName", memberRegReq.getMemberName());
        jsonObject.put("nick", memberRegReq.getNickName());
        jsonObject.put("dateOfBirth", memberRegReq.getBirthday());

        //M:已婚 S:未婚 D:离异 O:其他
        String marrage = "O";
        //0未婚1已婚2未知
        if (Objects.isNull(memberRegReq.getMarriageStatus())) {
            marrage = "O";
        } else if (memberRegReq.getMarriageStatus().equals(2)) {
            marrage = "O";
        } else if (memberRegReq.getMarriageStatus().equals(0)) {
            marrage = "S";
        } else if (memberRegReq.getMarriageStatus().equals(1)) {
            marrage = "M";
        }
        jsonObject.put("marriage", marrage);
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "dvf";
    }
}

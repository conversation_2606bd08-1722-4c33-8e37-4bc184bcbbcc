package com.shuyun.mariana.script.impl.whoo.memtag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegRsp;
import com.shuyun.cem.std.member.protocol.memtag.querymemtag.MemTagDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        List<MemTagDto> memTagDtos = new ArrayList<>();

        JSONArray dataArr = rspObj.getJSONArray("data");
        if (Objects.nonNull(dataArr)) {
            for (Integer i = 0 ; i < dataArr.size(); i++) {
                MemTagDto memTagDto = new MemTagDto();

                JSONObject inputParam = dataArr.getJSONObject(i);
                memTagDto.setTagId(inputParam.getString("tagId"));
                memTagDto.setName(inputParam.getString("tagName"));

                List<String> tagValues = new ArrayList<>();
                String [] tagsRtn = inputParam.getString("tagValues").split(",");
                if (Objects.nonNull(tagsRtn) && tagsRtn.length > 0) {
                    for (String tagRtn : tagsRtn) {
                        tagValues.add(tagRtn);
                    }
                }
                memTagDto.setTagValues(tagValues);

                memTagDtos.add(memTagDto);
            }
        }

        RestWrap<List<MemTagDto>> memTagQueryRestWrap = new RestWrap<>();
        memTagQueryRestWrap.buildSuccess(memTagDtos);

        String result = JSON.toJSONString(memTagQueryRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + ".memTag.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("memberId", "DVF20240711WX00000");
        dataObj.put("status", "REGISTERED");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"data\":[]}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

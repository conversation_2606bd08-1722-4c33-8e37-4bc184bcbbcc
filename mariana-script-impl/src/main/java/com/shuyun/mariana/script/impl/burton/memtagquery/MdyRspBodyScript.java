package com.shuyun.mariana.script.impl.burton.memtagquery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memtag.tagmeta.kylin.TagMetaDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        ArrayList<TagMetaDto> tagMetaDtos = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(rspBodyInJson);
        for (int i = 0; i < jsonArray.size(); i++) {
            TagMetaDto tagMetaDto = new TagMetaDto();
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            tagMetaDto.setId(jsonObject.getString("id"));
            tagMetaDto.setName(jsonObject.getString("name"));
            tagMetaDtos.add(tagMetaDto);
        }
        RestWrap<List<TagMetaDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(tagMetaDtos);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memTagQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[\n" +
                "    {\n" +
                "        \"id\": \"20240829172316372141847030231542\",\n" +
                "        \"name\": \"MM wave1 老会老客 低低高C\"\n" +
                "    }\n" +
                "]");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

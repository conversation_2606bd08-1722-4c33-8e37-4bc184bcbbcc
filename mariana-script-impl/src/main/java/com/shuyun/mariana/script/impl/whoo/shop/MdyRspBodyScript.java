package com.shuyun.mariana.script.impl.whoo.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        List<ShopDto> resultJsonArray = new ArrayList<>();
        PageWrap pageWrap = new PageWrap();
        JSONArray dataObj = rspObj.getJSONArray("data");
        if (Objects.nonNull(dataObj)) {
            for (int i = 0; i < dataObj.size(); i++) {
                ShopDto shopDto = new ShopDto();
                JSONObject jsonObj = dataObj.getJSONObject(i);

                //from tangming 或者你们可以这样判断,只用isValid 等于Y 的
                String isValid = jsonObj.getString("isValid");
                if (StringUtils.isNotEmpty(isValid) && "Y".equals(isValid)) {
                    shopDto.setShopCode(jsonObj.getString("shopCode"));
                    shopDto.setShopName(jsonObj.getString("shopName"));
                    shopDto.setStatus(jsonObj.getString("shopStatus"));
                    shopDto.setCityName(jsonObj.getString("cityName"));
                    shopDto.setAddress(jsonObj.getString("address"));
                    shopDto.setLatitude(jsonObj.getString("latitude"));
                    shopDto.setLongitude(jsonObj.getString("longitude"));
                    shopDto.setContactTel(jsonObj.getString("contactTel"));
                    shopDto.setOpenTime(jsonObj.getString("openDate"));
                    shopDto.setCloseTime(jsonObj.getString("closeDate"));
                    shopDto.setShopType(jsonObj.getString("shopType"));

                    String freeSpaStartTime = jsonObj.getString("freeSpaStartTime");
                    if (StringUtils.isNotEmpty(freeSpaStartTime)) {
                        freeSpaStartTime = freeSpaStartTime.substring(0, 5);
                        jsonObj.put("freeSpaStartTime", freeSpaStartTime);
                    }

                    String freeSpaEndTime = jsonObj.getString("freeSpaEndTime");
                    if (StringUtils.isNotEmpty(freeSpaEndTime)) {
                        freeSpaEndTime = freeSpaEndTime.substring(0, 5);
                        jsonObj.put("freeSpaEndTime", freeSpaEndTime);
                    }

                    shopDto.setProjectExt(JSON.toJSONString(jsonObj));
                    resultJsonArray.add(shopDto);
                }
            }
        }
        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".shop.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("page", "1");
        dataObj.put("pageSize", "10");
        dataObj.put("totalCount", "10");
        JSONArray array=new JSONArray();


        JSONObject dataObjItem = new JSONObject();
        dataObjItem.put("shopCode", "1");
        dataObjItem.put("shopName", "10");
        array.add(dataObjItem);
        dataObj.put("items", array);
        object.put("data", dataObj);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"0000000543\",\n" +
                "            \"shopCode\": \"0000000543\",\n" +
                "            \"memberType\": \"10\",\n" +
                "            \"shopName\": \"无锡商业大厦(OHUI)\",\n" +
                "            \"provinceCode\": \"29\",\n" +
                "            \"provinceName\": \"陕西省\",\n" +
                "            \"cityCode\": \"2903\",\n" +
                "            \"cityName\": \"宝鸡市\",\n" +
                "            \"districtCode\": \"290301\",\n" +
                "            \"districtName\": \"渭滨区\",\n" +
                "            \"address\": \"江苏省无锡市中山路343号  82766978\",\n" +
                "            \"contactTel\": \"0510-82765991\",\n" +
                "            \"status\": \"OPENING\",\n" +
                "            \"salOffCD\": \"1005\",\n" +
                "            \"salPartCD\": \"100505\",\n" +
                "            \"bizSalTpCD\": \"10\",\n" +
                "            \"freeSpaStartTime\": \"15:08:17\",\n" +
                "            \"freeSpaEndTime\": \"15:08:17\",\n" +
                "            \"lastSync\": \"2024-12-26T03:36:22.974Z\"\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

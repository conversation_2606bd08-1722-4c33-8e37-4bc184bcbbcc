package com.shuyun.mariana.script.impl.linqxuan.memdereg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memderegister.MemberDeRegisterRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=40
 */
public class MdyRspBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberDeRegisterRsp memberDeRegRsp = new MemberDeRegisterRsp();
        String memeberVipCode = rspObj.getString("data");
        if (StringUtils.isNotEmpty(memeberVipCode)) {
            memberDeRegRsp.setMemberId(memeberVipCode);
            memberDeRegRsp.setStatus("success");
        }

        RestWrap<MemberDeRegisterRsp> deRegRspRestWrap = new RestWrap<>();
        deRegRspRestWrap.buildSuccess(memberDeRegRsp);

        String result = JSON.toJSONString(deRegRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDeReg.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("status", "REGISTERED");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.dvf.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);
        //反序列化扩展字段
        JSONObject extObj = JSON.parseObject(memberDetailReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            Boolean kylinOpenIdQuery = extObj.getBoolean("kylinOpenIdQuery");
            ////通过openId查询，不需要UnionId查询
            if (kylinOpenIdQuery!=null&&kylinOpenIdQuery){
                memberDetailReq.setUnionId(null);
            }
            //通过UnionId 不需要openid查询
            if (kylinOpenIdQuery!=null&& !kylinOpenIdQuery){
                memberDetailReq.setOpenId(null);
            }
        }
        JSONObject jsonObject = buildCommon(memberDetailReq);

        List<String> optionalFields = new ArrayList<>();
        optionalFields.add("expirePoint");
        optionalFields.add("isWechatFans");
        optionalFields.add("color");
        optionalFields.add("profession");
        optionalFields.add("place");
        optionalFields.add("revenue");
        optionalFields.add("email");

        optionalFields.add("purchaseTimesInfor");
        optionalFields.add("acceptSourceInfor");
        optionalFields.add("followFactorInfor");
        optionalFields.add("preferBrandInfor");
        optionalFields.add("satisfactionInfor");
        jsonObject.put("optionalFields", optionalFields);
        jsonObject.put("channelDataRequired", true);
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.memDetail.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.shopquery;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap<ShopDto> shopDtoPageWrap = new PageWrap<>();
        ArrayList<ShopDto> shopDtos = new ArrayList<>();
        JSONArray content = rspObj.getJSONArray("data");
        for (int i = 0; i < content.size(); i++) {
            ShopDto shopDto = new ShopDto();
            JSONObject jsonObject = content.getJSONObject(i);
            shopDto.setShopCode(jsonObject.getString("shopCode"));
            shopDto.setShopName(jsonObject.getString("shopName"));
            shopDto.setShopType(jsonObject.getString("shopType"));
            shopDto.setProvinceName(jsonObject.getString("provinceName"));
            shopDto.setCityName(jsonObject.getString("cityName"));
            shopDto.setDistrictName(jsonObject.getString("districtName"));
            shopDto.setAddress(jsonObject.getString("address"));
            shopDto.setContactTel(jsonObject.getString("contactTel"));
            shopDto.setStatus(jsonObject.getString("status"));
            JSONArray picture = jsonObject.getJSONArray("picture");
            if (Objects.nonNull(picture)&&picture.size()>0) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("picture",JSON.toJSONString(picture));
                shopDto.setProjectExt(JSON.toJSONString(map));
                shopDto.setPicture(picture.getString(0));
            }
            shopDtos.add(shopDto);
        }
        shopDtoPageWrap.setItems(shopDtos);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(shopDtoPageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".shopQuery.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"currentPage\": 1,\n" +
                "    \"pageSize\": 20,\n" +
                "    \"totalCount\": 499,\n" +
                "    \"totalPage\": 25,\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"id\": \"0001\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0001\",\n" +
                "            \"shopName\": \"Beijing Longfusi\",\n" +
                "            \"shopType\": \"Flagship\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtName\": \"东城区\",\n" +
                "            \"address\": \"北京市东城区钱粮胡同38号20号楼一层BURTON店铺\",\n" +
                "            \"contactTel\": \"15001114210\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2024-12-05T09:15:00.622+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0002\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0002\",\n" +
                "            \"shopName\": \"Zhangjiakou Wanlong Resort\",\n" +
                "            \"shopType\": \"Snow Resort\",\n" +
                "            \"provinceName\": \"河北省\",\n" +
                "            \"cityName\": \"张家口市\",\n" +
                "            \"districtName\": \"崇礼区\",\n" +
                "            \"address\": \"张家口市崇礼区万龙滑雪场雪具大厅一楼Burton\",\n" +
                "            \"contactTel\": \"14756038684\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2024-12-05T09:15:00.571+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0003\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0003\",\n" +
                "            \"shopName\": \"BURTON STORE – Zhangjiakou Genting Resort\",\n" +
                "            \"shopType\": \"Snow Resort\",\n" +
                "            \"provinceName\": \"河北省\",\n" +
                "            \"cityName\": \"张家口市\",\n" +
                "            \"districtName\": \"崇礼区\",\n" +
                "            \"address\": \"张家口市崇礼区太子城村梧桐大道密苑云顶乐园burton店铺\",\n" +
                "            \"contactTel\": \"18650042701\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2024-12-05T09:15:00.528+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0004\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0004\",\n" +
                "            \"shopName\": \"太古里快闪店\",\n" +
                "            \"shopType\": \"POP-UPS\",\n" +
                "            \"provinceName\": \"天津\",\n" +
                "            \"cityName\": \"天津市\",\n" +
                "            \"districtName\": \"南开区\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"结束\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-10-12T09:00:03.632+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0006\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0006\",\n" +
                "            \"shopName\": \"Shanghai HKRI Taikoo Hui\",\n" +
                "            \"shopType\": \"Flagship\",\n" +
                "            \"provinceName\": \"上海\",\n" +
                "            \"cityName\": \"上海市\",\n" +
                "            \"districtName\": \"静安区\",\n" +
                "            \"address\": \"上海市石门一路180号兴业太古汇首层S107号铺\",\n" +
                "            \"contactTel\": \"15026706365\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2025-02-09T09:15:00.565+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0007\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0007\",\n" +
                "            \"shopName\": \"BURTON天津佛罗伦萨小镇奥莱店\",\n" +
                "            \"shopType\": \"POP-UPS\",\n" +
                "            \"provinceName\": \"天津\",\n" +
                "            \"cityName\": \"天津市\",\n" +
                "            \"districtName\": \"河北区\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"结束\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-10-12T09:00:03.573+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"0008\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"0008\",\n" +
                "            \"shopName\": \"Zhangjiakou Taiwoo Resort\",\n" +
                "            \"shopType\": \"Snow Resort\",\n" +
                "            \"provinceName\": \"河北省\",\n" +
                "            \"cityName\": \"张家口市\",\n" +
                "            \"districtName\": \"崇礼区\",\n" +
                "            \"address\": \"河北省张家口市崇礼区太舞小镇\",\n" +
                "            \"contactTel\": \"18650042701\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2025-03-26T09:15:00.111+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"12390487\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"12390487\",\n" +
                "            \"shopName\": \"BURTON官方旗舰店\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"JD\",\n" +
                "            \"lastSync\": \"2024-10-10T18:42:17.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"341152385\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"341152385\",\n" +
                "            \"shopName\": \"burton官方旗舰店\",\n" +
                "            \"status\": \"NOT_OPEN\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"Y\",\n" +
                "            \"channelType\": \"TAOBAO\",\n" +
                "            \"lastSync\": \"2022-08-07T04:21:05.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"58417\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"58417\",\n" +
                "            \"shopName\": \"四维驰-磁器口\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"107\",\n" +
                "            \"districtName\": \"东城区\",\n" +
                "            \"address\": \"北京东城区珠市口东大街2号大都市2-19\",\n" +
                "            \"contactTel\": \"13911293535/010-67012506\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"58419\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"58419\",\n" +
                "            \"shopName\": \"傲天极限雪具店-磁器口店\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"115\",\n" +
                "            \"districtName\": \"顺义区\",\n" +
                "            \"address\": \"北京市顺义区马坡镇庙卷村陈衙路8号 傲天极限装备库\",\n" +
                "            \"contactTel\": \"13811282133//18911450223\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"58420\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"58420\",\n" +
                "            \"shopName\": \"JJ滑雪（JJ SKI）\",\n" +
                "            \"provinceCode\": \"20\",\n" +
                "            \"provinceName\": \"广东省\",\n" +
                "            \"cityCode\": \"1728\",\n" +
                "            \"cityName\": \"广州市\",\n" +
                "            \"districtCode\": \"1414\",\n" +
                "            \"districtName\": \"花都区\",\n" +
                "            \"address\": \"广东省广州市花都区融创茂雪世界1006，BURTON专卖店\",\n" +
                "            \"contactTel\": \"159898007070\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"58423\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"58423\",\n" +
                "            \"shopName\": \"长春极限品牌体验店（长1）\",\n" +
                "            \"provinceCode\": \"8\",\n" +
                "            \"provinceName\": \"吉林省\",\n" +
                "            \"cityCode\": \"523\",\n" +
                "            \"cityName\": \"长春市\",\n" +
                "            \"districtCode\": \"98\",\n" +
                "            \"districtName\": \"南关区\",\n" +
                "            \"address\": \"吉林省长春市南关区南岭体育场9号门\",\n" +
                "            \"contactTel\": \"16604457224\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"61181\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"61181\",\n" +
                "            \"shopName\": \"傲天极限雪具店-崇礼店\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"115\",\n" +
                "            \"districtName\": \"顺义区\",\n" +
                "            \"address\": \"北京市顺义区马坡镇庙卷村陈衙路8号 傲天极限装备库\",\n" +
                "            \"contactTel\": \"13811282133//18911450223\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"61182\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"61182\",\n" +
                "            \"shopName\": \"傲天极限雪具店-万龙店\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"115\",\n" +
                "            \"districtName\": \"顺义区\",\n" +
                "            \"address\": \"北京市顺义区马坡镇庙卷村陈衙路8号 傲天极限装备库\",\n" +
                "            \"contactTel\": \"13811282133//18911450223\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"61183\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"61183\",\n" +
                "            \"shopName\": \"傲天极限雪具店-南山店\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"115\",\n" +
                "            \"districtName\": \"顺义区\",\n" +
                "            \"address\": \"北京市顺义区马坡镇庙卷村陈衙路8号 傲天极限装备库\",\n" +
                "            \"contactTel\": \"13811282133//18911450223\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"61184\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"61184\",\n" +
                "            \"shopName\": \"傲天极限雪具店-大同店\",\n" +
                "            \"provinceCode\": \"2\",\n" +
                "            \"provinceName\": \"北京\",\n" +
                "            \"cityCode\": \"2\",\n" +
                "            \"cityName\": \"北京市\",\n" +
                "            \"districtCode\": \"115\",\n" +
                "            \"districtName\": \"顺义区\",\n" +
                "            \"address\": \"北京市顺义区马坡镇庙卷村陈衙路8号 傲天极限装备库\",\n" +
                "            \"contactTel\": \"13811282133//18911450223\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"61409\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"61409\",\n" +
                "            \"shopName\": \"长春极限哈尔滨品牌体验店（长4）\",\n" +
                "            \"provinceCode\": \"9\",\n" +
                "            \"provinceName\": \"黑龙江省\",\n" +
                "            \"cityCode\": \"583\",\n" +
                "            \"cityName\": \"哈尔滨市\",\n" +
                "            \"districtCode\": \"691\",\n" +
                "            \"districtName\": \"南岗区\",\n" +
                "            \"address\": \"黑龙江省哈尔滨市南岗区淮河路349号\",\n" +
                "            \"contactTel\": \"16604457224\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"62531\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"62531\",\n" +
                "            \"shopName\": \"XGO烟台店\",\n" +
                "            \"provinceCode\": \"16\",\n" +
                "            \"provinceName\": \"山东省\",\n" +
                "            \"cityCode\": \"1250\",\n" +
                "            \"cityName\": \"烟台市\",\n" +
                "            \"districtCode\": \"1357\",\n" +
                "            \"districtName\": \"芝罘区\",\n" +
                "            \"address\": \"山东省烟台市芝罘区大马路21号\",\n" +
                "            \"contactTel\": \"18641199987\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        },\n" +
                "        {\n" +
                "            \"id\": \"62532\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"shopCode\": \"62532\",\n" +
                "            \"shopName\": \"XGO-鞍山店\",\n" +
                "            \"provinceCode\": \"7\",\n" +
                "            \"provinceName\": \"辽宁省\",\n" +
                "            \"cityCode\": \"446\",\n" +
                "            \"cityName\": \"鞍山市\",\n" +
                "            \"districtCode\": \"21\",\n" +
                "            \"districtName\": \"铁东区\",\n" +
                "            \"address\": \"辽宁省鞍山市铁东区汇园大道爱家皇家花园31-3号商铺\",\n" +
                "            \"contactTel\": \"18641199987\",\n" +
                "            \"status\": \"正常\",\n" +
                "            \"picture\": [],\n" +
                "            \"isValid\": \"正常\",\n" +
                "            \"channelType\": \"POS\",\n" +
                "            \"lastSync\": \"2022-08-23T19:34:53.000+08:00\",\n" +
                "            \"customizedProperties\": {}\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.miele.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.mempoint.MemberPointQueryRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        //非标准接口，直接是数据
        MemberPointQueryRsp memberPointQueryRsp = new MemberPointQueryRsp();
        /*JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            memberPointQueryRsp = JSON.parseObject(JSON.toJSONString(dataObj), MemberPointQueryRsp.class);
        }
*/
        Integer point = rspObj.getInteger("point");
        memberPointQueryRsp.setPoint(point);
        RestWrap<MemberPointQueryRsp> restWrap = new RestWrap<>();
        restWrap.buildSuccess(memberPointQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memPoint.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {

        JSONObject dataObj = new JSONObject();
        dataObj.put("point", 456);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, dataObj.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

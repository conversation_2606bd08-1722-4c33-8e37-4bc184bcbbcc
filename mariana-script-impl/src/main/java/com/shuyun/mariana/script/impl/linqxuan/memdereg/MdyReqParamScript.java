package com.shuyun.mariana.script.impl.linqxuan.memdereg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=242
 */
public class MdyReqParamScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        JSONObject param = JSON.parseObject(reqBodyInJson);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        //查询条件类型 1按手机号；2按会员卡号
        addParameters.put("type", "1");
        addParameters.put("filterValue", param.getString("mobile"));
        addParameters.put("vipSeriesCode", "001");
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memDeReg.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("tenant", "tenant");
        object.put("unionId", "unionId");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.guide;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap pageWrap = new PageWrap<>();
        RestWrap<PageWrap> restWrap = new RestWrap<>();
        JSONArray content = rspObj.getJSONArray("jsondata");
        HashMap<String, Object> hashMap = new HashMap<>();
        for (int i = 0; i < content.size(); i++) {
            JSONObject jsonObject = content.getJSONObject(i);
             hashMap.put("projectExt",JSON.toJSONString(jsonObject));
             hashMap.put("name",jsonObject.getString("guideName"));
             hashMap.put("qrCodeUrl",jsonObject.getString("cpQrCode"));
             hashMap.put("avatar",jsonObject.getString("headImgUrl"));
            break;
        }
        pageWrap.setItems(Arrays.asList(hashMap));
        restWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".guide.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"code\": 200,\n" +
                "    \"msg\": \"操作成功！\",\n" +
                "    \"jsondata\": [\n" +
                "        {\n" +
                "            \"id\": \"afea0a54c0b645b0b5e3ff6d015fcde3\",\n" +
                "            \"memberType\": \"BURTON\",\n" +
                "            \"guideCode\": \"15100267306\",\n" +
                "            \"deleted\": \"false\",\n" +
                "            \"guideName\": \"高超\",\n" +
                "            \"createTime\": \"2024-02-03T21:00:39.287Z\",\n" +
                "            \"updateTime\": \"2024-02-03T21:00:39.287Z\",\n" +
                "            \"lastTime\": \"2240-08-07T07:02:51.560Z\",\n" +
                "            \"mobile\": \"15100267306\",\n" +
                "            \"active\": \"true\",\n" +
                "            \"job\": \"SHOP_MANAGE\",\n" +
                "            \"headImgUrl\": \"https://wework.qpic.cn/wwpic/118994_UCSHN0xtRlWICRe_1695226056/0\",\n" +
                "            \"employment\": \"true\",\n" +
                "            \"employeeTime\": \"2024-02-03T21:00:39.287Z\",\n" +
                "            \"cpUserId\": \"15100267306\",\n" +
                "            \"cpQrCodeMediaId\": \"prx00i6K5HvraeDMkdIynCIn2z-84Rren8AAU4xSo2YvsmzMYjB-YTn_qBO_Jd0K\",\n" +
                "            \"cpQrCodeMediaCreateTime\": \"2024-02-03T21:00:39.287Z\",\n" +
                "            \"mpQrCode\": \"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHc8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyVXRERkVCdmJkUjMxMDAwMDAwN2sAAgRwFdZkAwQAAAAA\",\n" +
                "            \"mpQrCodePlainText\": \"http://weixin.qq.com/q/02UtDFEBvbdR310000007k\",\n" +
                "            \"cpQrCode\": \"https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc6f60cc04bf436b8b\",\n" +
                "            \"mpQrCodeMediaId\": \"36Bc_jmvRVc6D7m2C6QdgFVOCnlA-SLynyIFMoHpYbU3XaCIBVbt3uRipZ4nQMgw8\",\n" +
                "            \"guideId\": \"214\"\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

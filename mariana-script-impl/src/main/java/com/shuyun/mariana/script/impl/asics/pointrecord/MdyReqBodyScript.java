package com.shuyun.mariana.script.impl.asics.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.pointrecord.MemPointRecordQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分流水查询请求体转换脚本
 * 功能：将内部标准MemPointRecordQueryReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemPointRecordQueryReq pointRecordQueryReq = JSON.parseObject(reqBodyInJson, MemPointRecordQueryReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        
        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();
        
        //解析配置信息
        String appCfgJson = pointRecordQueryReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        
        String bizExtJson = pointRecordQueryReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 映射到ASICS字段格式 - 支持积分流水查询相关字段
        
        // 会员标识 - 优先使用memberId，如果没有则使用其他标识
        asicsObject.put("memberCode", inputParam.getString("memberId"));
        
        // 时间范围
        if (StringUtils.isNotEmpty(pointRecordQueryReq.getStartTime())) {
            asicsObject.put("startDate", pointRecordQueryReq.getStartTime());
        } else if (StringUtils.isNotEmpty(inputParam.getString("startDate"))) {
            asicsObject.put("startDate", inputParam.getString("startDate"));
        }
        
        if (StringUtils.isNotEmpty(pointRecordQueryReq.getEndTime())) {
            asicsObject.put("endDate", pointRecordQueryReq.getEndTime());
        } else if (StringUtils.isNotEmpty(inputParam.getString("endDate"))) {
            asicsObject.put("endDate", inputParam.getString("endDate"));
        }
        
        // 分页参数
        if (pointRecordQueryReq.getPage() != null) {
            asicsObject.put("page", pointRecordQueryReq.getPage());
        } else if (inputParam.containsKey("page") && inputParam.get("page") != null) {
            asicsObject.put("page", inputParam.getInteger("page"));
        } else {
            asicsObject.put("page", 0); // 默认第一页
        }
        
        if (pointRecordQueryReq.getPageSize() != null) {
            asicsObject.put("size", pointRecordQueryReq.getPageSize());
        } else if (inputParam.containsKey("size") && inputParam.get("size") != null) {
            asicsObject.put("size", inputParam.getInteger("size"));
        } else {
            asicsObject.put("size", 20); // 默认每页20条
        }
        
        // 排序参数
        if (inputParam.containsKey("sort") && inputParam.get("sort") != null) {
            asicsObject.put("sort", inputParam.getInteger("sort"));
        } else {
            asicsObject.put("sort", 0); // 默认排序
        }
        
        // 从输入参数中直接获取ASICS特有字段
        if (StringUtils.isNotEmpty(inputParam.getString("memberCode"))) {
            asicsObject.put("memberCode", inputParam.getString("memberCode"));
        }
        if (StringUtils.isNotEmpty(inputParam.getString("startDate"))) {
            asicsObject.put("startDate", inputParam.getString("startDate"));
        }
        if (StringUtils.isNotEmpty(inputParam.getString("endDate"))) {
            asicsObject.put("endDate", inputParam.getString("endDate"));
        }
        
        // 设置渠道信息和会员系统ID - 使用基类方法
        setChannel(asicsObject, appCfgObj, inputParam);
        setMembershipSystemId(asicsObject, appCfgObj, inputParam);

        String result = asicsObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".pointRecord.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("memberCode", "ASICS_CODE_001");
        object.put("startDate", "2024-01-01");
        object.put("endDate", "2024-12-31");
        object.put("page", 0);
        object.put("size", 20);
        object.put("sort", 0);
        object.put("startTime", "2024-01-01 00:00:00");
        object.put("endTime", "2024-12-31 23:59:59");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"memberCode\":\"ASICS_EXT_001\",\"pointRecordQueryType\":\"ALL\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.meilv.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMeiLvScriptBase {
    @Override
    public String run(String rspBodyInJson) {
        JSONObject resultObj=new JSONObject();
        JSONArray items = JSON.parseArray(rspBodyInJson);
        if (items!=null){
            List<String> deductList=new ArrayList<>();
            deductList.add("DEDUCT");
            deductList.add("REVERSE_DEDUCT");
            deductList.add("OPEN_FREEZE");
            deductList.add("FREEZE");
            deductList.add("SPECIAL_DEDUCT");
            deductList.add("SPECIAL_FREEZE");
            deductList.add("EXPIRE");
            deductList.add("ABOLISH");
            deductList.add("RECALCULATE");
            deductList.add("SPECIAL_ABOLISH");
            deductList.add("MANUAL_ABOLISH");
            for (int i = 0; i < items.size(); i++) {
                JSONObject jsonObj = items.getJSONObject(i);
                try {
                    String changeTime = jsonObj.getString("changeTime");
                    if (changeTime!=null){
                        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                        Date formattedDate = dateFormat.parse(changeTime);
                        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String formattedDateStr = dateFormat2.format(formattedDate);
                        jsonObj.put("changeTime",formattedDateStr);
                    }
                    String changeType = jsonObj.getString("changeType");
                    jsonObj.put("recordType",changeType);
                    String desc = jsonObj.getString("description");
                    jsonObj.put("description",desc);
                    String effectiveDate = jsonObj.getString("effectiveTime");
                    jsonObj.put("effectTime",effectiveDate);
                    String overdueDate = jsonObj.getString("expiredTime");
                    jsonObj.put("expiredTime",overdueDate);
                    BigDecimal point = jsonObj.getBigDecimal("point");
                    if (point!=null&&changeType!=null&&!"".equalsIgnoreCase(changeType)&&deductList.contains(changeType)){
                        if (BigDecimal.ZERO.compareTo(point)<0){
                            jsonObj.put("point",point.multiply(new BigDecimal("-1")));
                        }
                    }
                }catch (Exception e){

                }
            }
        }else {
            items=new JSONArray();
        }

        resultObj.put("items",items);
        resultObj.put("totalPage",1);
        resultObj.put("totalCount",items.size());
        RestWrap<Map<String,Object>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(resultObj);
        String result = JSON.toJSONString(restWrap);
        return result;
    }
    @Override
    public String groovyFileName() {
         return projectDir()+".pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("changeType", "SEND");
        itemObj.put("point", 500.0);
        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[{\"changeTime\":\"2024-12-17T14:29:10.912+08:00\",\"traceId\":\"202412170003\",\"totalPoint\":28.0,\"integral\":2.0,\"changeType\":\"DEDUCT\",\"channel\":\"WECHAT\",\"description\":\"积分扣减\",\"changeMode\":\"INTERFACE\",\"id\":\"6c862fe85604443b8b26d6296c085bb2\",\"point\":2,\"key\":\"202412170003\",\"memberId\":\"f2a6df361f5a45688020cb6683912335\"}]");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

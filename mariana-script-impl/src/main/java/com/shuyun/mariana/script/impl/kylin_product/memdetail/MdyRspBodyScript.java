package com.shuyun.mariana.script.impl.kylin_product.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseKylinProScriptBase {

    public String cusRun(String reqBodyInJson, String rspBodyInJson) {
        //success	string	接口返回代码, s: 正常; e: 错误; f: 失败
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        String errorCode = rspObj.getString("error_code");
        if (StringUtils.isNotEmpty(errorCode)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("errorCode"));
            errRestWrap.setMessage(rspObj.getString("msg"));
            return JSON.toJSONString(errRestWrap);
        }

        return curDoRun(reqBodyInJson, rspObj);
    }

    public String curDoRun(String reqBodyInJson, JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        JSONArray jsonArray = rspObj.getJSONArray("data");
        if (Objects.nonNull(jsonArray) && !jsonArray.isEmpty()) {
            //reqBodyInJson
            MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);
            JSONObject inputObj = JSON.parseObject(reqBodyInJson);

            //获取查询类型，区分memberId，openId还是 unnionId查询
            String bizExtJson = memberDetailReq.getBizExtJson();
            String queryType = null;
            if (StringUtils.isNotEmpty(bizExtJson)) {
                JSONObject bizJson = JSON.parseObject(bizExtJson);
                queryType = bizJson.getString("queryType");
            }

            //openId查询
            String appId = inputObj.getString("appId");
            String openId = inputObj.getString("openId");
            String unnionId = inputObj.getString("unionId");

            //基于会员ID查询时找到第一个
            String memberId = inputObj.getString("memberId");
            if (StringUtils.isNotEmpty(memberId)) {
                //遍历数组,如果先基于appId匹配，匹配到直接返回
                Boolean find = false;
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject itemObj = jsonArray.getJSONObject(i);
                    String channelStatus = itemObj.getString("channelStatus");
                    String toAppId = itemObj.getString("appId");
                    //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                    if ("ACTIVE".equals(channelStatus) && StringUtils.isNotEmpty(toAppId) && toAppId.equals(appId)) {
                        buildMemRsp(memberDetailRsp, itemObj);
                        find = true;
                        break;
                    }
                }

                //基于appId未找到，则找第一个状态正常得会员
                if (!find) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject itemObj = jsonArray.getJSONObject(i);
                        String channelStatus = itemObj.getString("channelStatus");
                        //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                        if ("ACTIVE".equals(channelStatus)) {
                            buildMemRsp(memberDetailRsp, itemObj);
                            break;
                        }
                    }
                }
            } else {
                //unionID查询
                if (StringUtils.isNotEmpty(queryType) && queryType.equals("unionId")) {
                    //遍历数组
                    Boolean find = false;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject itemObj = jsonArray.getJSONObject(i);
                        String channelStatus = itemObj.getString("channelStatus");
                        String toUnnionId = itemObj.getString("unionId");
                        //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                        if ("ACTIVE".equals(channelStatus) && StringUtils.isNotEmpty(toUnnionId) && toUnnionId.equals(unnionId)) {
                            buildMemRsp(memberDetailRsp, itemObj);
                            find = true;
                            break;
                        }
                    }
                } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile")) {
                    //遍历数组,如果先基于appId匹配，匹配到直接返回
                    Boolean find = false;
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject itemObj = jsonArray.getJSONObject(i);
                        String channelStatus = itemObj.getString("channelStatus");
                        String toAppId = itemObj.getString("appId");
                        //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                        if ("ACTIVE".equals(channelStatus) && StringUtils.isNotEmpty(toAppId) && toAppId.equals(appId)) {
                            buildMemRsp(memberDetailRsp, itemObj);
                            find = true;
                            break;
                        }
                    }

                    //基于appId未找到，则找第一个状态正常得会员
                    if (!find) {
                        for (int i = 0; i < jsonArray.size(); i++) {
                            JSONObject itemObj = jsonArray.getJSONObject(i);
                            String channelStatus = itemObj.getString("channelStatus");
                            //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                            if ("ACTIVE".equals(channelStatus)) {
                                buildMemRsp(memberDetailRsp, itemObj);
                                break;
                            }
                        }
                    }
                } else {
                    //遍历数组
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject itemObj = jsonArray.getJSONObject(i);
                        String channelStatus = itemObj.getString("channelStatus");
                        String toOpenId = itemObj.getString("openId");
                        //兼容解绑情况，ACTIVE:生效/已绑定；INACTIVE:失效/已解绑；FROZEN:冻结
                        if ("ACTIVE".equals(channelStatus) && StringUtils.isNotEmpty(toOpenId) && toOpenId.equals(openId)) {
                            buildMemRsp(memberDetailRsp, itemObj);
                            break;
                        }
                    }
                }
            }
        }

        RestWrap<MemberDetailRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    private void buildMemRsp(MemberDetailRsp memberDetailRsp, JSONObject jsonObject) {
        memberDetailRsp.setMobile(jsonObject.getString("mobile"));
        memberDetailRsp.setMemberName(jsonObject.getString("fullName"));
        memberDetailRsp.setBirthday(jsonObject.getString("dateOfBirth"));
        memberDetailRsp.setGender(jsonObject.getString("gender"));
        memberDetailRsp.setHeadImgUrl(jsonObject.getString("headImgUrl"));
        memberDetailRsp.setMemberId(jsonObject.getString("memberId"));
        //处理optionalFieldData
        JSONObject optFieldObj = jsonObject.getJSONObject("optionalFieldData");
        if (null != optFieldObj) {
            Map<String, Object> optFieldMap = optFieldObj.toJavaObject(Map.class);
            for (String key : optFieldMap.keySet()) {
                jsonObject.put(key, optFieldMap.get(key));
            }
        }

        memberDetailRsp.setProjectExt(JSON.toJSONString(jsonObject));
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDetail.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, "{\n" +
                "        \"appId\": \"wxdd0a621948ccc675\",\n" +
                "        \"unionId\": \"obm3r0eKtP-0uQsOwcRZbQIMHT9M5\",\n" +
                "        \"openId\": \"oP9385YvYe7FjF3VjiCSNEjTIQT8\"\n" +
                "    }");
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"error_code\": null,\n" +
                "    \"msg\": \"success\",\n" +
                "    \"data\": [\n" +
                "        {\n" +
                "            \"memberId\": \"WHOOCWPFHZ\",\n" +
                "            \"id\": \"14472131177a4729b163a52b475bcf65\",\n" +
                "            \"brand\": \"whoo\",\n" +
                "            \"enrollChannel\": \"WECHAT\",\n" +
                "            \"enrollShopCode\": null,\n" +
                "            \"enrollShopName\": null,\n" +
                "            \"ascriptionShopCode\": null,\n" +
                "            \"ascriptionShopName\": null,\n" +
                "            \"ascriptionChannel\": null,\n" +
                "            \"ascriptionChannelName\": null,\n" +
                "            \"fullName\": \"张佩佩\",\n" +
                "            \"gender\": \"M\",\n" +
                "            \"mobile\": \"13554771609\",\n" +
                "            \"email\": null,\n" +
                "            \"dateOfBirth\": \"2025-03-04\",\n" +
                "            \"marriage\": null,\n" +
                "            \"customerNo\": \"wxdd0a621948ccc675_oP9385YvYe7FjF3VjiCSNEjTIQT8\",\n" +
                "            \"unionId\": \"oxv_b1VlZw0Wrd6Mn3ePu3JUBoig\",\n" +
                "            \"appType\": null,\n" +
                "            \"appId\": \"wxdd0a621948ccc675\",\n" +
                "            \"openId\": \"oP9385YvYe7FjF3VjiCSNEjTIQT8\",\n" +
                "            \"headImgUrl\": \"https://shuyun-cms-prod.oss-cn-zhangjiakou.aliyuncs.com/upload/shuyun/8608606333/20250310/ed4f5321106f4effa74e38d5a21606e3.jpg\",\n" +
                "            \"mixMobile\": null,\n" +
                "            \"nick\": null,\n" +
                "            \"triggerTime\": \"2025-03-15T07:13:32.000Z\",\n" +
                "            \"channelStatus\": \"INACTIVE\",\n" +
                "            \"action\": \"UNBIND\",\n" +
                "            \"optionalFieldData\": {\n" +
                "                \"skinCareDesc\": null,\n" +
                "                \"address\": null,\n" +
                "                \"serviceGuide\": null\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"memberId\": \"WHOOCWPFHZ\",\n" +
                "            \"id\": \"44cdd31463f54180aaba86684ec8a709\",\n" +
                "            \"brand\": \"whoo\",\n" +
                "            \"enrollChannel\": \"WECHAT\",\n" +
                "            \"enrollShopCode\": \"wxdd0a621948ccc675\",\n" +
                "            \"enrollShopName\": \"微信会员中心\",\n" +
                "            \"ascriptionShopCode\": null,\n" +
                "            \"ascriptionShopName\": null,\n" +
                "            \"ascriptionChannel\": null,\n" +
                "            \"ascriptionChannelName\": null,\n" +
                "            \"fullName\": \"张佩佩\",\n" +
                "            \"gender\": \"M\",\n" +
                "            \"mobile\": \"13554771609\",\n" +
                "            \"email\": null,\n" +
                "            \"dateOfBirth\": \"2025-03-04\",\n" +
                "            \"marriage\": null,\n" +
                "            \"customerNo\": \"wxdd0a621948ccc675_oP9385YvYe7FjF3VjiCSNEjTIQT8\",\n" +
                "            \"unionId\": \"oxv_b1VlZw0Wrd6Mn3ePu3JUBoig\",\n" +
                "            \"appType\": null,\n" +
                "            \"appId\": \"wxdd0a621948ccc675\",\n" +
                "            \"openId\": \"oP9385YvYe7FjF3VjiCSNEjTIQT8\",\n" +
                "            \"headImgUrl\": null,\n" +
                "            \"mixMobile\": null,\n" +
                "            \"nick\": null,\n" +
                "            \"triggerTime\": \"2025-03-15T07:29:42.650Z\",\n" +
                "            \"channelStatus\": \"ACTIVE\",\n" +
                "            \"action\": \"BIND\",\n" +
                "            \"optionalFieldData\": {\n" +
                "                \"skinCareDesc\": null,\n" +
                "                \"address\": null,\n" +
                "                \"serviceGuide\": null\n" +
                "            }\n" +
                "        }\n" +
                "    ]\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("cusRun", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

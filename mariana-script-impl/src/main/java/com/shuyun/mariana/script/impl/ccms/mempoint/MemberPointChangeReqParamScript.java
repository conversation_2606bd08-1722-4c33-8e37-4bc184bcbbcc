package com.shuyun.mariana.script.impl.ccms.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * https://open.shuyun.com/#/apidoc?type=41&apiId=34
 */
public class MemberPointChangeReqParamScript extends BaseCcmsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        //反序列化为协议
        JSONObject jsonObject = JSON.parseObject(reqBodyInJson);
        if (jsonObject.get("source")==null || "".equals(jsonObject.getString("source"))){
            jsonObject.put("source","OTHER");
        }
        if (jsonObject.get("created")==null || "".equals(jsonObject.getString("created"))){
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formattedDate = dateFormat.format(new Date());
            jsonObject.put("created",formattedDate);
        }
        if (jsonObject.get("pointChangeType")!=null&& !"".equals(jsonObject.getString("pointChangeType"))){
            String pointChangeType = jsonObject.getString("pointChangeType");
            BigDecimal changePoint = jsonObject.getBigDecimal("changePoint");
            if ("POINT_CONSUME".equalsIgnoreCase(pointChangeType)
                    ||"POINT_FREEZE".equalsIgnoreCase(pointChangeType)){
                if (changePoint!=null&& BigDecimal.ZERO.compareTo(changePoint)<0){
                    jsonObject.put("changePoint",changePoint.multiply(new BigDecimal("-1")));
                }
            }
        }
        //消费积分转换为负数
        //构建通用
        //会员基础业务数据填充
        jsonObject.put("id", jsonObject.getString("openId"));

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memberPointChange.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("sequence", "sequence");
        object.put("expired", "expired");
        object.put("created", "created");
        object.put("openId", "id");
        object.put("source", "source");
        object.put("changePoint", 90);
        object.put("operator", "operator");
        object.put("desc", "desc");
        object.put("pointChangeType", "POINT_CONSUME");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.burton.memduepoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        Map<String,Object> resultMap=new HashMap<>();
        Map<String,Object> projectExt=new HashMap<>();
        if (Objects.nonNull(rspObj)) {
            projectExt.put("code",rspObj.get("code"));
            if (Objects.nonNull(rspObj.get("msg"))){
               projectExt.put("msg",rspObj.get("msg"));
           }else {
               projectExt.put("totalAmount",rspObj.get("totalAmount"));
               projectExt.put("requiredAmount",rspObj.get("requiredAmount"));
           }
        }
        resultMap.put("projectExt",JSON.toJSONString(projectExt));
        RestWrap<Map<String,Object>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultMap);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+ ".memDuePoint.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("msg", "购买伯峻任意课程或产品达到下一等级");
        object.put("code", "testCode");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

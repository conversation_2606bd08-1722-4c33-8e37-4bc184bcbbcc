package com.shuyun.mariana.script.impl.linqxuan.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 *
 *
 */
public class MdyReqBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberRegReq);

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberRegReq);

        //填充ID和 unnionId
        //平台客户账号
        jsonObject.put("tel", memberRegReq.getMobile());
        jsonObject.put("customerName", memberRegReq.getMemberName());
        jsonObject.put("headimg", memberRegReq.getHeadImgUrl());
        jsonObject.put("sex", memberRegReq.getGender());
        jsonObject.put("nickname", memberRegReq.getNickName());
        jsonObject.put("birthday", memberRegReq.getBirthday());

        //注册regShopCode
        jsonObject.put("regShopCode", "DZSW083");

        /**
         * authContextList
         * {
         "vKey": "oJetv5FQLZfGITcPtNAEpNDIL91AsjdfklsdjfklsdjkfldfjkljklF",
         "appId": "wxae04852152b9f536",
         "type": "KT006"
         },
         {
         "vKey": "oJetv5FQLZfGITcPtNAEpNDIL91F",
         "appId": "wxae04852152b9f536",
         "type": "KT007"
         }
         */
        JSONArray authContextArr = new JSONArray();

        JSONObject KT006Obj = new JSONObject();
        KT006Obj.put("type", "KT006");
        KT006Obj.put("appId", memberRegReq.getAppId());
        KT006Obj.put("vKey", memberRegReq.getUnionId());
        authContextArr.add(KT006Obj);

        JSONObject KT007Obj = new JSONObject();
        KT007Obj.put("type", "KT007");
        KT007Obj.put("appId", memberRegReq.getAppId());
        KT007Obj.put("vKey", memberRegReq.getOpenId());
        authContextArr.add(KT007Obj);
        jsonObject.put("authContextList", authContextArr);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

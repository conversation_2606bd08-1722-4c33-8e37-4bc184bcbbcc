package com.shuyun.mariana.script.impl.linqxuan.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap pageWrap = new PageWrap();
        List<OrderDto> orderDtos = new ArrayList<>();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONObject pageObj = dataObj.getJSONObject("page");
            if (Objects.nonNull(pageObj)) {
                pageWrap.setTotalCount(pageObj.getInteger("total"));
            }

            JSONArray itemArrObj = dataObj.getJSONArray("rows");
            if (Objects.nonNull(itemArrObj)) {
                for (int i = 0; i < itemArrObj.size(); i++) {

                    //组装订单
                    JSONObject orderObj = itemArrObj.getJSONObject(i);
                    OrderDto orderDto = new OrderDto();
                    orderDto.setOrderId(orderObj.getString("relationCode"));
                    orderDto.setShopName(orderObj.getString("shop"));
                    orderDto.setShopCode(orderObj.getString("shopCode"));
                    orderDto.setOrderTime(orderObj.getString("addTime"));
                    orderDto.setPayment(orderObj.getDouble("realPrice"));
//                    orderDto.setOrderTime(orderObj.getString("createTime"));
                    orderDto.setTotalFee(orderObj.getDouble("moTotalOrder"));
                    orderDto.setProjectExt(JSON.toJSONString(pageObj));

                    orderDtos.add(orderDto);
                }
            }
        }

        pageWrap.setItems(orderDtos);
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memOrder.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String orderRsp = "{\n" +
                "    \"code\": \"\",\n" +
                "    \"data\": {\n" +
                "        \"page\": {\n" +
                "            \"page\": 1,\n" +
                "            \"pageSize\": 1000,\n" +
                "            \"pageTotal\": 1,\n" +
                "            \"total\": 2\n" +
                "        },\n" +
                "        \"rows\": [\n" +
                "            {\n" +
                "                \"area\": \"上海区域\",\n" +
                "                \"auditStatus\": \"AT003\",\n" +
                "                \"auditStatusName\": \"客审通过\",\n" +
                "                \"billDate\": \"2024-10-21 00:00:00\",\n" +
                "                \"billNo\": \"LSSHDMSD027P20241021145883\",\n" +
                "                \"confirmTime\": \"2024-10-21 17:24:38\",\n" +
                "                \"createTime\": \"2024-10-21 17:24:41\",\n" +
                "                \"dvyType\": \"物流配送\",\n" +
                "                \"emCode\": \"306031\",\n" +
                "                \"emId\": 101550,\n" +
                "                \"employee\": \"306031_王丽华\",\n" +
                "                \"expandField1\": \"\",\n" +
                "                \"expandField10\": \"\",\n" +
                "                \"expandField2\": \"\",\n" +
                "                \"expandField3\": \"\",\n" +
                "                \"expandField4\": \"\",\n" +
                "                \"expandField5\": \"\",\n" +
                "                \"expandField6\": \"\",\n" +
                "                \"expandField7\": \"\",\n" +
                "                \"expandField8\": \"\",\n" +
                "                \"expandField9\": \"\",\n" +
                "                \"isDistribute\": false,\n" +
                "                \"isFrozen\": false,\n" +
                "                \"isSettle\": false,\n" +
                "                \"isUpload\": false,\n" +
                "                \"lockDistribute\": false,\n" +
                "                \"moPaid\": 115.0,\n" +
                "                \"moTotalDue\": 115.0,\n" +
                "                \"moTotalOrder\": 115.0,\n" +
                "                \"moTotalRetail\": 115.0,\n" +
                "                \"phone\": \"18710426223\",\n" +
                "                \"qty\": 2.0,\n" +
                "                \"rootno\": \"LSSHDMSD027P20241021145883\",\n" +
                "                \"salesOrderId\": \"aee3799daad84024940218240a6e8ee6\",\n" +
                "                \"shopCode\": \"SHDMSD027P\",\n" +
                "                \"shopId\": 604,\n" +
                "                \"shopName\": \"总部形象店\",\n" +
                "                \"soType\": \"SORGTYPE04\",\n" +
                "                \"soTypeName\": \"ONEPOS\",\n" +
                "                \"sourceNo\": \"\",\n" +
                "                \"sourceType\": \"osto_receipt\",\n" +
                "                \"status\": \"BS002\",\n" +
                "                \"statusName\": \"已付款\",\n" +
                "                \"thirdNo\": \"\",\n" +
                "                \"vipCode\": \"899242607660498944\",\n" +
                "                \"vipId\": 29096634\n" +
                "            },\n" +
                "            {\n" +
                "                \"area\": \"上海区域\",\n" +
                "                \"auditStatus\": \"AT003\",\n" +
                "                \"auditStatusName\": \"客审通过\",\n" +
                "                \"billDate\": \"2024-10-21 00:00:00\",\n" +
                "                \"billNo\": \"LSSHDMSD027P20241021145884\",\n" +
                "                \"confirmTime\": \"2024-10-21 17:26:28\",\n" +
                "                \"createTime\": \"2024-10-21 17:26:29\",\n" +
                "                \"dvyType\": \"物流配送\",\n" +
                "                \"emCode\": \"306031\",\n" +
                "                \"emId\": 101550,\n" +
                "                \"employee\": \"306031_王丽华\",\n" +
                "                \"expandField1\": \"\",\n" +
                "                \"expandField10\": \"\",\n" +
                "                \"expandField2\": \"\",\n" +
                "                \"expandField3\": \"\",\n" +
                "                \"expandField4\": \"\",\n" +
                "                \"expandField5\": \"\",\n" +
                "                \"expandField6\": \"\",\n" +
                "                \"expandField7\": \"\",\n" +
                "                \"expandField8\": \"\",\n" +
                "                \"expandField9\": \"\",\n" +
                "                \"isDistribute\": false,\n" +
                "                \"isFrozen\": false,\n" +
                "                \"isSettle\": false,\n" +
                "                \"isUpload\": false,\n" +
                "                \"lockDistribute\": false,\n" +
                "                \"moPaid\": 1998.0,\n" +
                "                \"moTotalDue\": 1998.0,\n" +
                "                \"moTotalOrder\": 1998.0,\n" +
                "                \"moTotalRetail\": 1998.0,\n" +
                "                \"phone\": \"18710426223\",\n" +
                "                \"qty\": 2.0,\n" +
                "                \"rootno\": \"LSSHDMSD027P20241021145884\",\n" +
                "                \"salesOrderId\": \"de12fab253184387aefeed6551b3286b\",\n" +
                "                \"shopCode\": \"SHDMSD027P\",\n" +
                "                \"shopId\": 604,\n" +
                "                \"shopName\": \"总部形象店\",\n" +
                "                \"soType\": \"SORGTYPE04\",\n" +
                "                \"soTypeName\": \"ONEPOS\",\n" +
                "                \"sourceNo\": \"\",\n" +
                "                \"sourceType\": \"osto_receipt\",\n" +
                "                \"status\": \"BS002\",\n" +
                "                \"statusName\": \"已付款\",\n" +
                "                \"thirdNo\": \"\",\n" +
                "                \"vipCode\": \"899242607660498944\",\n" +
                "                \"vipId\": 29096634\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"message\": \"\",\n" +
                "    \"success\": \"s\"\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, orderRsp);
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

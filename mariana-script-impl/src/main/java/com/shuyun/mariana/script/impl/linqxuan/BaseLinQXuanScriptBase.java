package com.shuyun.mariana.script.impl.linqxuan;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;

import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 */
public abstract class BaseLinQXuanScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        //success	string	接口返回代码, s: 正常; e: 错误; f: 失败
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        String success = rspObj.getString("success");
        if (!"s".equals(success)) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
//        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("source", "SRC008");
        jsonObject.put("regShopCode", "DZSW083");
        jsonObject.put("vipSeriesCode", "001");
        //来源销售平台代码，比如SY- 数云，LJ-丽晶，WM-微盟
        jsonObject.put("soType", "SY");
        return jsonObject;
    }

    public JSONObject buildSimpleCommon(JSONObject inputBody, BaseMember baseMember) {
        //copy通用变量
//        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
        JSONObject jsonObject = new JSONObject();
        inputBody.put("regShopCode", "DZSW083");
        inputBody.put("vipSeriesCode", "001");
        //来源销售平台代码，比如SY- 数云，LJ-丽晶，WM-微盟
        inputBody.put("soType", "SY");
        return inputBody;
    }

    public void buildMemberBiz(JSONObject jsonObject, BaseMember baseMember) {
        JSONObject extObj = JSON.parseObject(baseMember.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String vipCode = extObj.getString("vipCode");
            jsonObject.put("vipCode", vipCode);
        }
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "linqxuan";
    }
}

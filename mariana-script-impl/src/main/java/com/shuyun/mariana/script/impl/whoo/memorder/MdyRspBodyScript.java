package com.shuyun.mariana.script.impl.whoo.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.cem.std.member.protocol.memorder.OrderItemDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<PageWrap<OrderDto>> restWrap = new RestWrap<>();

        JSONArray dataArr = rspObj.getJSONArray("data");
        List<OrderDto> orderDtos = new ArrayList<>();
        if (Objects.nonNull(dataArr) && dataArr.size() > 0) {
            for (Integer i = 0 ; i < dataArr.size(); i++) {
                JSONObject orderObj = dataArr.getJSONObject(i);

                OrderDto orderDto = new OrderDto();
                orderDto.setPlatCode(orderObj.getString("channelType"));
                orderDto.setShopCode(orderObj.getString("shopCode"));
                orderDto.setShopName(orderObj.getString("shopName"));
                Date orderTime = orderObj.getDate("orderTime");
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String formattedDateStr = dateFormat.format(orderTime);
                orderDto.setOrderTime(formattedDateStr);
                orderDto.setOrderStatus(orderObj.getString("orderStatus"));
                orderDto.setPayment(orderObj.getDouble("payment"));
                orderDto.setOrderId(orderObj.getString("orderId"));

                //子订单信息
                List<OrderItemDto> orderItems = new ArrayList<>();
                JSONArray itemArr = orderObj.getJSONArray("orderItems");
                if (Objects.nonNull(itemArr) && itemArr.size() > 0) {
                    for (Integer j = 0; j < itemArr.size(); j++) {
                        OrderItemDto orderItemDto = new OrderItemDto();

                        JSONObject itemObj = itemArr.getJSONObject(j);

                        orderItemDto.setProductCode(itemObj.getString("productCode"));
                        orderItemDto.setProductName(itemObj.getString("productName"));
                        orderItemDto.setQuantity(itemObj.getInteger("quantity"));
                        orderItemDto.setPayment(itemObj.getDouble("payment"));

                        orderItems.add(orderItemDto);
                    }
                }

                orderDto.setOrderItems(orderItems);

                orderDtos.add(orderDto);
            }
        }


        PageWrap<OrderDto> pageWrap = new PageWrap<>();
        pageWrap.setItems(orderDtos);
        restWrap.buildSuccess(pageWrap);

        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + ".memOrder.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("shopTypeCode", "taobao");

        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);

        JSONObject pageObj = new JSONObject();
        pageObj.put("page", 0);
        pageObj.put("pageSize", 20);
        pageObj.put("totalCount", 2);
        pageObj.put("items", itemArr);

        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        object.put("data", pageObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"currentPage\":1,\"pageSize\":50,\"totalCount\":3,\"totalPage\":1,\"data\":[{\"channelType\":\"WECHAT\",\"couponFee\":15.0,\"couponNo\":\"C123456\",\"customerNo\":\"customerNo5\",\"description\":\"description\",\"discountFee\":577.26,\"discountRate\":0.38,\"freight\":10.0,\"guideCode\":\"001\",\"memberId\":\"WHOOP1ZG34\",\"memberType\":\"whoo\",\"orderId\":\"TEST_20241213114447\",\"orderStatus\":\"FINISHED\",\"orderTime\":\"2024-12-13T11:44:47+08:00\",\"orderType\":\"NORMAL\",\"payTime\":\"2024-12-13T11:44:47+08:00\",\"payment\":353.24,\"receiveTime\":\"2024-12-13T11:44:47+08:00\",\"receiverAddress\":\"receiverAddress\",\"receiverCity\":\"receiverCity\",\"receiverDistrict\":\"receiverDistrict\",\"receiverMobile\":\"receiverMobile\",\"receiverName\":\"receiverName\",\"receiverProvince\":\"receiverProvince\",\"receiverTelephone\":\"receiverTelephone\",\"receiverZipCode\":\"receiverZipCode\",\"shippingTime\":\"2024-12-13T11:44:47+08:00\",\"shopCode\":\"SHOP001\",\"shopName\":\"SHOP001\",\"shopTypeCode\":\"shopTypeCode\",\"totalFee\":930.5,\"totalQuantity\":1,\"orderItems\":[{\"channelType\":\"TAOBAO\",\"discountFee\":577.26,\"discountRate\":0.38,\"finishTime\":\"2024-12-13T11:44:47+08:00\",\"orderItemId\":\"TEST_20241213114447_1\",\"orderTime\":\"2024-12-13T11:44:47+08:00\",\"orderType\":\"NORMAL\",\"payment\":353.24,\"picture\":[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"],\"productCode\":\"P001\",\"productName\":\"P001\",\"quantity\":1,\"retailPrice\":100.0,\"shopTypeCode\":\"shopTypeCode\",\"skuId\":\"S001\",\"status\":\"FINISHED\",\"tagPrice\":100.0,\"totalFee\":930.5,\"customizedProperties\":{}}],\"updateTime\":\"2024-12-13T11:44:51.173+08:00\",\"lastSync\":\"2024-12-13T11:44:51.173+08:00\",\"customizedProperties\":{}},{\"channelType\":\"WECHAT\",\"couponFee\":15.0,\"couponNo\":\"C123456\",\"customerNo\":\"customerNo5\",\"description\":\"description\",\"discountFee\":118.19,\"discountRate\":0.67,\"freight\":10.0,\"guideCode\":\"001\",\"memberId\":\"WHOOP1ZG34\",\"memberType\":\"whoo\",\"orderId\":\"TEST_20241213114443\",\"orderStatus\":\"FINISHED\",\"orderTime\":\"2024-12-13T11:44:43+08:00\",\"orderType\":\"NORMAL\",\"payTime\":\"2024-12-13T11:44:43+08:00\",\"payment\":240.36,\"receiveTime\":\"2024-12-13T11:44:43+08:00\",\"receiverAddress\":\"receiverAddress\",\"receiverCity\":\"receiverCity\",\"receiverDistrict\":\"receiverDistrict\",\"receiverMobile\":\"receiverMobile\",\"receiverName\":\"receiverName\",\"receiverProvince\":\"receiverProvince\",\"receiverTelephone\":\"receiverTelephone\",\"receiverZipCode\":\"receiverZipCode\",\"shippingTime\":\"2024-12-13T11:44:43+08:00\",\"shopCode\":\"SHOP001\",\"shopName\":\"SHOP001\",\"shopTypeCode\":\"shopTypeCode\",\"totalFee\":358.55,\"totalQuantity\":1,\"orderItems\":[{\"channelType\":\"TAOBAO\",\"discountFee\":118.19,\"discountRate\":0.67,\"finishTime\":\"2024-12-13T11:44:43+08:00\",\"orderItemId\":\"TEST_20241213114443_1\",\"orderTime\":\"2024-12-13T11:44:43+08:00\",\"orderType\":\"NORMAL\",\"payment\":240.36,\"picture\":[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"],\"productCode\":\"P001\",\"productName\":\"P001\",\"quantity\":1,\"retailPrice\":100.0,\"shopTypeCode\":\"shopTypeCode\",\"skuId\":\"S001\",\"status\":\"FINISHED\",\"tagPrice\":100.0,\"totalFee\":358.55,\"customizedProperties\":{}}],\"updateTime\":\"2024-12-13T11:44:47.776+08:00\",\"lastSync\":\"2024-12-13T11:44:47.776+08:00\",\"customizedProperties\":{}},{\"channelType\":\"WECHAT\",\"couponFee\":15.0,\"couponNo\":\"C123456\",\"customerNo\":\"customerNo5\",\"description\":\"description\",\"discountFee\":196.0,\"discountRate\":0.59,\"freight\":10.0,\"guideCode\":\"001\",\"memberId\":\"WHOOP1ZG34\",\"memberType\":\"whoo\",\"orderId\":\"TEST_20241213114441\",\"orderStatus\":\"FINISHED\",\"orderTime\":\"2024-12-13T11:44:41+08:00\",\"orderType\":\"NORMAL\",\"payTime\":\"2024-12-13T11:44:41+08:00\",\"payment\":282.05,\"receiveTime\":\"2024-12-13T11:44:41+08:00\",\"receiverAddress\":\"receiverAddress\",\"receiverCity\":\"receiverCity\",\"receiverDistrict\":\"receiverDistrict\",\"receiverMobile\":\"receiverMobile\",\"receiverName\":\"receiverName\",\"receiverProvince\":\"receiverProvince\",\"receiverTelephone\":\"receiverTelephone\",\"receiverZipCode\":\"receiverZipCode\",\"shippingTime\":\"2024-12-13T11:44:41+08:00\",\"shopCode\":\"SHOP001\",\"shopName\":\"SHOP001\",\"shopTypeCode\":\"shopTypeCode\",\"totalFee\":478.05,\"totalQuantity\":1,\"orderItems\":[{\"channelType\":\"TAOBAO\",\"discountFee\":196.0,\"discountRate\":0.59,\"finishTime\":\"2024-12-13T11:44:41+08:00\",\"orderItemId\":\"TEST_20241213114441_1\",\"orderTime\":\"2024-12-13T11:44:41+08:00\",\"orderType\":\"NORMAL\",\"payment\":282.05,\"picture\":[\"https://allpro.saas.top/FsB-uzzX4WtCzpDu-Vuwqp2OdNbQ?e=1547742055&token=yNjhOp7gnH4St9yP72OlwuQ6JUNQf49pxAuGWYFt:1eZIUjRKIekGuMGG-c-s7UqNF1Y=;\"],\"productCode\":\"P001\",\"productName\":\"P001\",\"quantity\":1,\"retailPrice\":100.0,\"shopTypeCode\":\"shopTypeCode\",\"skuId\":\"S001\",\"status\":\"FINISHED\",\"tagPrice\":100.0,\"totalFee\":478.05,\"customizedProperties\":{}}],\"updateTime\":\"2024-12-13T11:44:45.201+08:00\",\"lastSync\":\"2024-12-13T11:44:45.201+08:00\",\"customizedProperties\":{}}]}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.ccmsv2;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 * ccms v2开始对接sapi
 */
public abstract class BaseCcmsV2ScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        Boolean success = rspObj.getBoolean("success");
        if (!success) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));

        return jsonObject;
    }

    public void buildMemQuery(JSONObject jsonObject, MemberDetailReq memberDetailReq) {
        String appCfgJson = memberDetailReq.getAppCfgJson();
        JSONObject appCfgObj = new JSONObject();
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }

        //租户
        String tenant = appCfgObj.getString("tenant");
        if (StringUtils.isNotEmpty(memberDetailReq.getTenant())) {
            tenant = memberDetailReq.getTenant();
        }
        jsonObject.put("tenant", tenant);

        //卡ID
        String cardId = appCfgObj.getString("cardPlanId");
        if (StringUtils.isNotEmpty(memberDetailReq.getCardPlanId())) {
            cardId = memberDetailReq.getCardPlanId();
        }
        jsonObject.put("cardPlanId", cardId);

        //平台
        String platCode = appCfgObj.getString("platCode");
        if (StringUtils.isNotEmpty(memberDetailReq.getPlatCode())) {
            platCode = memberDetailReq.getPlatCode();
        }
        jsonObject.put("platCode", platCode);

        //shopId
        String shopId = memberDetailReq.getAppId();
        if (StringUtils.isNotEmpty(memberDetailReq.getShopId())) {
            shopId = memberDetailReq.getShopId();
        }
        jsonObject.put("shopId", shopId);
    }

    public String buildMemRsp(JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            memberDetailRsp = JSON.parseObject(JSON.toJSONString(dataObj), MemberDetailRsp.class);
            String name = dataObj.getString("name");
            //赢家没有nick，都是用会员名字填充
            memberDetailRsp.setMemberName(name);
            memberDetailRsp.setNickname(name);
            memberDetailRsp.setProjectExt(JSON.toJSONString(dataObj));
        }

        RestWrap<MemberDetailRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {

    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "ccmsV2";
    }
}

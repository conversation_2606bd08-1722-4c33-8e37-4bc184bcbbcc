package com.shuyun.mariana.script.impl.asics;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.UUID;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 */
public abstract class BaseAsicsScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        Boolean success = rspObj.getBoolean("success");
        if (!success) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("msg"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
        return jsonObject;
    }

    public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
        jsonObject.put("fullName", memberRegReq.getMemberName());
        jsonObject.put("nick", memberRegReq.getNickName());
        jsonObject.put("dateOfBirth", memberRegReq.getBirthday());

        //M:已婚 S:未婚 D:离异 O:其他
        String marrage = "O";
        //0未婚1已婚2未知
        if (Objects.isNull(memberRegReq.getMarriageStatus())) {
            marrage = "O";
        } else if (memberRegReq.getMarriageStatus().equals(2)) {
            marrage = "O";
        } else if (memberRegReq.getMarriageStatus().equals(0)) {
            marrage = "S";
        } else if (memberRegReq.getMarriageStatus().equals(1)) {
            marrage = "M";
        }
        jsonObject.put("marriage", marrage);
    }

    /**
     * 设置ASICS会员系统ID
     * 优先级：appCfgObj.membershipSystemId > inputParam.membershipSystemId > 默认值"1"
     *
     * @param asicsObject 输出对象
     * @param appCfgObj 应用配置对象
     * @param inputParam 输入参数对象
     */
    public void setMembershipSystemId(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
        String membershipSystemId = "1"; // 默认值

        // 优先从appCfgObj获取
        if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("membershipSystemId"))) {
            membershipSystemId = appCfgObj.getString("membershipSystemId");
        }
        // 其次从inputParam获取
        else if (inputParam != null && inputParam.containsKey("membershipSystemId") && inputParam.get("membershipSystemId") != null) {
            membershipSystemId = inputParam.getString("membershipSystemId");
        }

        asicsObject.put("membershipSystemId", membershipSystemId);
    }

    /**
     * 设置ASICS渠道信息
     * 优先级：appCfgObj.channel > inputParam.channel > 默认值"15"
     *
     * @param asicsObject 输出对象
     * @param appCfgObj 应用配置对象
     * @param inputParam 输入参数对象
     */
    public void setChannel(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
        String channel = "15"; // 默认值

        // 优先从appCfgObj获取
        if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("channel"))) {
            channel = appCfgObj.getString("channel");
        }
        // 其次从inputParam获取
        else if (inputParam != null && inputParam.containsKey("channel") && inputParam.get("channel") != null) {
            channel = inputParam.getString("channel");
        }

        asicsObject.put("channel", channel);
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "asics";
    }
}

package com.shuyun.mariana.script.impl.whoo.goods;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqParamScript extends BaseWhooScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        //反序列化为协议
        BaseMember baseMember = JSON.parseObject(reqBodyInJson, BaseMember.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();

        //反序列化 ext_cfg，得到programCode
        String appCfgJson = baseMember.getAppCfgJson();
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            addParameters.put("programCode", appCfgObj.getString("programCode"));
        } else {
            addParameters.put("programCode", "whoo");
        }
        addParameters.put("page", String.valueOf(baseMember.getPage()));
        addParameters.put("pageSize", String.valueOf(baseMember.getPageSize()));
        String productId = inputParam.getString("productId");
        if (StringUtils.isNotEmpty(productId)) {
            addParameters.put("productCode", productId);
        }

        //from kylin channelType传CRM或者

        String bizJson = baseMember.getBizExtJson();
        if (StringUtils.isNotEmpty(bizJson)) {
            JSONObject bizObj = JSON.parseObject(bizJson);
            String goodsChannel = bizObj.getString("goodsChannel");
            addParameters.put("channelType", goodsChannel);
        }
        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "goodsQuery.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("tenant", "tenant");
        object.put("unionId", "unionId");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

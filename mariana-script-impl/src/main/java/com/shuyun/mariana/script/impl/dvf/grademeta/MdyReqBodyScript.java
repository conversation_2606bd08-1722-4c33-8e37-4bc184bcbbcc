package com.shuyun.mariana.script.impl.dvf.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaQueryReq;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        GradeMetaQueryReq gradeMetaQueryReq = JSON.parseObject(reqBodyInJson, GradeMetaQueryReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(gradeMetaQueryReq);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.gradeMeta.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @IgnoreGen
    public static void main(String[] args) {
        MdyReqBodyScript mdyReqBodyScript = new MdyReqBodyScript();
        mdyReqBodyScript.genReqBodyScript(true);
    }
}

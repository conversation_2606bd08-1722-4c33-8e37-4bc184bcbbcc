package com.shuyun.mariana.script.impl.asics.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS等级配置查询响应体转换脚本
 * 功能：将ASICS系统的响应转换为内部标准GradeMetaDto协议
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        RestWrap<List<GradeMetaDto>> restWrap = new RestWrap<>();
        // 处理ASICS等级配置响应数据
        List<GradeMetaDto> gradeMetaDtos = new ArrayList<>();
        JSONArray dataArray = rspObj.getJSONArray("data");
        
        if (Objects.nonNull(dataArray) && dataArray.size() > 0) {
            for (int i = 0; i < dataArray.size(); i++) {
                JSONObject gradeObj = dataArray.getJSONObject(i);
                
                GradeMetaDto gradeMetaDto = new GradeMetaDto();
                
                // 映射ASICS字段到标准GradeMetaDto字段
                if (gradeObj.containsKey("gradeId")) {
                    gradeMetaDto.setGradeId(gradeObj.getString("gradeId"));
                }
                
                if (gradeObj.containsKey("gradeName")) {
                    gradeMetaDto.setGradeName(gradeObj.getString("gradeName"));
                }
                
                if (gradeObj.containsKey("gradeAmount")) {
                    String gradeAmount = gradeObj.getString("gradeAmount");
                    if (StringUtils.isNotEmpty(gradeAmount)) {
                        try {
                            gradeMetaDto.setGradeAmount(Double.valueOf(gradeAmount));
                        } catch (NumberFormatException e) {
                            // 如果转换失败，设置为0
                            gradeMetaDto.setGradeAmount(0.0);
                        }
                    }
                }
                
                if (gradeObj.containsKey("sortId")) {
                    gradeMetaDto.setGradeSort(gradeObj.getInteger("sortId"));
                }
                
                // 保存原始数据作为扩展信息
                gradeMetaDto.setProjectExt(JSON.toJSONString(gradeObj));
                
                gradeMetaDtos.add(gradeMetaDto);
            }
        }
        
        // 按sortId排序
        gradeMetaDtos.sort((g1, g2) -> {
            Integer sort1 = g1.getGradeSort();
            Integer sort2 = g2.getGradeSort();
            if (sort1 == null) sort1 = 0;
            if (sort2 == null) sort2 = 0;
            return sort1.compareTo(sort2);
        });

        restWrap.buildSuccess(gradeMetaDtos);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".gradeMeta.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        // 构建ASICS格式的测试响应数据
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("errorCode", "");
        object.put("errorMessage", "");
        object.put("timeStamp", System.currentTimeMillis());

        // 构建等级配置数据
        JSONArray dataArray = new JSONArray();
        
        // 等级1 - 普通会员
        JSONObject grade1 = new JSONObject();
        grade1.put("gradeId", 1);
        grade1.put("gradeName", "普通会员");
        grade1.put("gradeAmount", "0");
        grade1.put("sortId", 1);
        dataArray.add(grade1);
        
        // 等级2 - 银卡会员
        JSONObject grade2 = new JSONObject();
        grade2.put("gradeId", 2);
        grade2.put("gradeName", "银卡会员");
        grade2.put("gradeAmount", "1000");
        grade2.put("sortId", 2);
        dataArray.add(grade2);
        
        // 等级3 - 金卡会员
        JSONObject grade3 = new JSONObject();
        grade3.put("gradeId", 3);
        grade3.put("gradeName", "金卡会员");
        grade3.put("gradeAmount", "5000");
        grade3.put("sortId", 3);
        dataArray.add(grade3);
        
        // 等级4 - 钻石会员
        JSONObject grade4 = new JSONObject();
        grade4.put("gradeId", 4);
        grade4.put("gradeName", "钻石会员");
        grade4.put("gradeAmount", "10000");
        grade4.put("sortId", 4);
        dataArray.add(grade4);
        
        object.put("data", dataArray);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

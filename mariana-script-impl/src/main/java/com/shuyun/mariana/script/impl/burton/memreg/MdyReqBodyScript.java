package com.shuyun.mariana.script.impl.burton.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.WxAddressDto;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseBurtonScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(memberRegReq));
        jsonObject.put("appId", memberRegReq.getAppId());
        jsonObject.put("appType", "WECHAT_MINI_PROGRAM");
        jsonObject.put("memberType", "BURTON");
        jsonObject.put("mobile", memberRegReq.getMobile());
        jsonObject.put("openId", memberRegReq.getOpenId());
        jsonObject.put("unionId", memberRegReq.getUnionId());
        if (StringUtils.isNotEmpty(memberRegReq.getGender())) {
            jsonObject.put("gender  ", memberRegReq.getGender());
        }else {
            jsonObject.put("gender  ", "O");
        }
        //非必填
        if (StringUtils.isNotEmpty(memberRegReq.getHeadImgUrl())){
            jsonObject.put("headImgUrl",memberRegReq.getHeadImgUrl());
        }
        if (StringUtils.isNotEmpty(memberRegReq.getBirthday())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate parsed = LocalDate.parse(memberRegReq.getBirthday(), formatter);
            jsonObject.put("birthYear", parsed.getYear());
            DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("MM-dd");
            jsonObject.put("birthDay",dateTimeFormatter.format(parsed) );
        }
        jsonObject.put("memberName", memberRegReq.getMemberName());
        jsonObject.put("nickname", memberRegReq.getNickName());
        WxAddressDto dftAddress = memberRegReq.getDftAddress();
        if (dftAddress!=null&&dftAddress.getProvinceName()!=null&&!"".equalsIgnoreCase(dftAddress.getProvinceName())){
            jsonObject.put("province", dftAddress.getProvinceName());
        }
        if (dftAddress!=null&&dftAddress.getCityName()!=null&&!"".equalsIgnoreCase(dftAddress.getCityName())){
            jsonObject.put("city", dftAddress.getCityName());
        }
        if (dftAddress!=null&&dftAddress.getCountyName()!=null&&!"".equalsIgnoreCase(dftAddress.getCountyName())){
            jsonObject.put("district", dftAddress.getCountyName());
        }
        jsonObject.put("shopCode","MembershipWechatApp");
        JSONObject extObj = JSON.parseObject(memberRegReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            JSONObject object = new JSONObject();
            String skiTime = extObj.getString("skiTime");
            if (StringUtils.isNotEmpty(skiTime)) {
                object.put("skiTime",skiTime);
            }
            if (StringUtils.isNotEmpty(extObj.getString("salesrepId"))) {
                object.put("salesrepId",extObj.getString("salesrepId"));
            }
            if (Objects.nonNull(object)) {
                jsonObject.put("customizedProperties", object);
            }
            if (StringUtils.isNotEmpty(extObj.getString("shopCode"))) {
                jsonObject.put("shopCode",extObj.getString("shopCode"));
            }

        }

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir()+".memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("gender", "F");
        object.put("birthday", "2025-04-27");
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

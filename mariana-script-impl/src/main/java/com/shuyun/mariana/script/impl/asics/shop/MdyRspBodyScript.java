package com.shuyun.mariana.script.impl.asics.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.shop.ShopDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        List<ShopDto> resultJsonArray = new ArrayList<>();
        PageWrap pageWrap = new PageWrap();
        JSONArray dataObj = rspObj.getJSONArray("data");
        if (Objects.nonNull(dataObj)) {
            for (int i = 0; i < dataObj.size(); i++) {
                ShopDto shopDto = new ShopDto();
                JSONObject jsonObj = dataObj.getJSONObject(i);

                //from tangming 鎴栬€呬綘浠彲浠ヨ繖鏍峰垽鏂?鍙敤isValid 绛変簬Y 鐨?
                String isValid = jsonObj.getString("isValid");
                if (StringUtils.isNotEmpty(isValid) && "Y".equals(isValid)) {
                    shopDto.setShopCode(jsonObj.getString("shopCode"));
                    shopDto.setShopName(jsonObj.getString("shopName"));
                    shopDto.setStatus(jsonObj.getString("shopStatus"));
                    shopDto.setCityName(jsonObj.getString("cityName"));
                    shopDto.setAddress(jsonObj.getString("address"));
                    shopDto.setLatitude(jsonObj.getString("latitude"));
                    shopDto.setLongitude(jsonObj.getString("longitude"));
                    shopDto.setContactTel(jsonObj.getString("contactTel"));
                    shopDto.setOpenTime(jsonObj.getString("openDate"));
                    shopDto.setCloseTime(jsonObj.getString("closeDate"));
                    shopDto.setShopType(jsonObj.getString("shopType"));

                    String freeSpaStartTime = jsonObj.getString("freeSpaStartTime");
                    if (StringUtils.isNotEmpty(freeSpaStartTime)) {
                        freeSpaStartTime = freeSpaStartTime.substring(0, 5);
                        jsonObj.put("freeSpaStartTime", freeSpaStartTime);
                    }

                    String freeSpaEndTime = jsonObj.getString("freeSpaEndTime");
                    if (StringUtils.isNotEmpty(freeSpaEndTime)) {
                        freeSpaEndTime = freeSpaEndTime.substring(0, 5);
                        jsonObj.put("freeSpaEndTime", freeSpaEndTime);
                    }

                    shopDto.setProjectExt(JSON.toJSONString(jsonObj));
                    resultJsonArray.add(shopDto);
                }
            }
        }
        pageWrap.setItems(resultJsonArray);
        RestWrap<PageWrap<ShopDto>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(pageWrap);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".shop.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("page", "1");
        dataObj.put("pageSize", "10");
        dataObj.put("totalCount", "10");
        JSONArray array=new JSONArray();


        JSONObject dataObjItem = new JSONObject();
        dataObjItem.put("shopCode", "1");
        dataObjItem.put("shopName", "10");
        array.add(dataObjItem);
        dataObj.put("items", array);
        object.put("data", dataObj);
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //鐢熸垚鑴氭湰
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //娴嬭瘯鑴氭湰
        absScriptGenerate.testScript(scriptContext);
    }
}


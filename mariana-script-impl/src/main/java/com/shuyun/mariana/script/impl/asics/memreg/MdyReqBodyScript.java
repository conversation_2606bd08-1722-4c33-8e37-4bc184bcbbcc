package com.shuyun.mariana.script.impl.asics.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员注册请求体转换脚本
 * 功能：将内部标准MemberRegReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBodyInJson, MemberRegReq.class);
        
        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();
        
        //解析配置信息
        String appCfgJson = memberRegReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        
        String bizExtJson = memberRegReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 映射到ASICS字段格式
        // 店铺代码 - 从bizExtJson中获取，如果没有则使用默认值
        if (bizExtObj != null && StringUtils.isNotEmpty(bizExtObj.getString("qwStoreCode"))) {
            asicsObject.put("qwStoreCode", bizExtObj.getString("qwStoreCode"));
        } else {
            asicsObject.put("qwStoreCode", "5499"); // 默认店铺代码
        }
        
        // 基础会员信息
        asicsObject.put("unionId", memberRegReq.getUnionId());
        asicsObject.put("phone", memberRegReq.getMobile());
        asicsObject.put("nickName", memberRegReq.getNickName());
        
        // 性别转换：M->男, F->女, 其他->未知
        String gender = memberRegReq.getGender();
        String sexValue = "未知";
        if ("M".equals(gender) || "男".equals(gender)) {
            sexValue = "男";
        } else if ("F".equals(gender) || "女".equals(gender)) {
            sexValue = "女";
        }
        asicsObject.put("sex", sexValue);

        //生日
        asicsObject.put("birthday", memberRegReq.getBirthday());
        
        // 渠道信息 - 使用基类方法设置
        setChannel(asicsObject, appCfgObj, null);
        
        // 会员姓名
        asicsObject.put("name", memberRegReq.getMemberName());
        
        // 微信小程序OpenId
        asicsObject.put("wxShopOpenId", memberRegReq.getOpenId());
        
        // 扩展属性映射
        JSONObject extendAttributeMap = new JSONObject();
        if (bizExtObj != null) {
            // 注册时间
            if (StringUtils.isNotEmpty(bizExtObj.getString("registerTime"))) {
                extendAttributeMap.put("registerTime", bizExtObj.getString("registerTime"));
            } else {
                // 如果没有传入注册时间，使用当前时间
                java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                extendAttributeMap.put("registerTime", sdf.format(new java.util.Date()));
            }
            
            // 其他扩展字段
            if (appCfgObj != null && appCfgObj.containsKey("optionalFields")) {
                JSONArray optionalFields = appCfgObj.getJSONArray("optionalFields");
                if (optionalFields != null) {
                    for (int i = 0; i < optionalFields.size(); i++) {
                        String fieldName = optionalFields.getString(i);
                        if (StringUtils.isNotEmpty(bizExtObj.getString(fieldName))) {
                            extendAttributeMap.put(fieldName, bizExtObj.getString(fieldName));
                        }
                    }
                }
            }
        } else {
            // 默认注册时间
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            extendAttributeMap.put("registerTime", sdf.format(new java.util.Date()));
        }
        asicsObject.put("extendAttributeMap", extendAttributeMap);

        // 会员系统ID - 使用基类方法设置
        setMembershipSystemId(asicsObject, appCfgObj, null);

        String result = asicsObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberName", "糖晓甜");
        object.put("nickName", "糖x晓甜");
        object.put("gender", "F");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\"}");
        object.put("bizExtJson","{\"gender\":\"女\",\"qwStoreCode\":\"5499\",\"registerTime\":\"2024-09-17 16:28:38\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

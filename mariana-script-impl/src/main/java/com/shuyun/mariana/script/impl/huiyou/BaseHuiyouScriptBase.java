package com.shuyun.mariana.script.impl.huiyou;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;

import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/15 10:22
 */
public abstract class BaseHuiyouScriptBase extends AbsScriptGenerate {

    @Override
    public String run(String rspBodyInJson) {
        JSONObject rspObj = JSON.parseObject(rspBodyInJson);
        Boolean success = rspObj.getBoolean("success");
        if (!success) {
            RestWrap errRestWrap = new RestWrap();
            errRestWrap.setSuccess(false);
            errRestWrap.setCode(rspObj.getString("code"));
            errRestWrap.setMessage(rspObj.getString("message"));
            return JSON.toJSONString(errRestWrap);
        }

        return doRun(rspObj);
    }

    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    public JSONObject buildCommon(BaseMember baseMember) {
        //copy通用变量
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));

        return jsonObject;
    }

    public void buildMemberBiz(JSONObject reqBodyObj, Map<String, String> addParameters) {
        JSONObject appCfg = JSON.parseObject(reqBodyObj.getString("appCfgJson"));
        addParameters.put("_biz", appCfg.getString("_biz"));
    }

    @Override
    @IgnoreGen
    public String projectDir() {
        return "huiyou";
    }
}

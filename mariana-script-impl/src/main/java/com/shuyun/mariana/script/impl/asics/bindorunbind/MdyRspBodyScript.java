package com.shuyun.mariana.script.impl.asics.bindorunbind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memunbind.MemberUnbindRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员解绑响应体转换脚本
 * 功能：将ASICS系统的响应转换为内部标准MemberUnbindRsp协议
 */
public class MdyRspBodyScript extends BaseAsicsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberUnbindRsp memberUnbindRsp = new MemberUnbindRsp();
        
        // 处理ASICS系统的响应数据
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            memberUnbindRsp.setMemberId(dataObj.getString("memberCode"));
            // 保存原始响应数据作为扩展信息
            memberUnbindRsp.setProjectExt(JSON.toJSONString(dataObj));
        }

        memberUnbindRsp.setStatus("success");

        // 构建标准响应格式
        RestWrap<MemberUnbindRsp> unbindRspRestWrap = new RestWrap<>();
        unbindRspRestWrap.buildSuccess(memberUnbindRsp);

        String result = JSON.toJSONString(unbindRspRestWrap);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memUnbind.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "0");
        object.put("message", "success");
        
        JSONObject dataObj = new JSONObject();
        dataObj.put("status", "success");
        dataObj.put("unbindResult", true);
        dataObj.put("memberId", "ASICS20240917001");
        dataObj.put("customerNo", "ASICS_CUSTOMER_001");
        dataObj.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        dataObj.put("unbindTime", "2024-09-17 16:28:38");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

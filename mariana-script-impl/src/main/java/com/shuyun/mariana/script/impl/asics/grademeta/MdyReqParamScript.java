package com.shuyun.mariana.script.impl.asics.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS等级配置查询请求参数转换脚本
 * 功能：设置等级配置查询请求的HTTP方法和参数
 */
public class MdyReqParamScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        GradeMetaQueryReq gradeMetaQueryReq = JSON.parseObject(reqBodyInJson, GradeMetaQueryReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        
        // 等级配置查询使用 GET 方法
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();

        // membershipSystemId 是必须参数
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            String membershipSystemId = appCfgObj.getString("membershipSystemId");
            if (StringUtils.isNotEmpty(membershipSystemId)) {
                addParameters.put("membershipSystemId", membershipSystemId);
            } else {
                addParameters.put("membershipSystemId", "1"); // 默认值
            }
            
            // 其他配置参数
            String programCode = appCfgObj.getString("programCode");
            if (StringUtils.isNotEmpty(programCode)) {
                addParameters.put("programCode", programCode);
            }
        } else {
            addParameters.put("membershipSystemId", "1"); // 默认值
        }
        
        // 从输入参数中直接获取membershipSystemId（优先级更高）
        if (inputParam.containsKey("membershipSystemId")) {
            addParameters.put("membershipSystemId", inputParam.getString("membershipSystemId"));
        }

        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".gradeMeta.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("membershipSystemId", "1");
        object.put("token", "ASICS_TOKEN_123456");
        object.put("appCfgJson","{\"membershipSystemId\":\"1\",\"programCode\":\"ASICS001\",\"apiKey\":\"ASICS_API_KEY_001\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.linqxuan.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=36
 */
public class MdyReqBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);
        //构建通用
        JSONObject jsonObject = new JSONObject();
        //jasmine要求传null，是必选字段
        jsonObject.put("pkid", null);

        JSONObject dataObj = new JSONObject();
        jsonObject.put("data", dataObj);

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberMdyReq);
        //反序列化扩展字段
        JSONObject extObj = JSON.parseObject(memberMdyReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String vipCode = extObj.getString("vipCode");
            dataObj.put("vipCode", vipCode);
            dataObj.put("changeType", extObj.getString("changeType"));
            dataObj.put("value", extObj.getString("value"));
            dataObj.put("oldValue", extObj.getString("oldValue"));
        }
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memMdy.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberName", "test1");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

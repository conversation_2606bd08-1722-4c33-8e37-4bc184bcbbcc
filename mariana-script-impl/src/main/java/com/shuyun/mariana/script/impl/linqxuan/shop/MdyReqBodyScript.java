package com.shuyun.mariana.script.impl.linqxuan.shop;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.shop.ShopQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 *
 *
 */
public class MdyReqBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        ShopQueryReq shopQueryReq = JSON.parseObject(reqBodyInJson, ShopQueryReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(shopQueryReq);

        jsonObject.put("page", shopQueryReq.getPage());
        jsonObject.put("pageSize", shopQueryReq.getPageSize());

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, shopQueryReq);

        JSONObject extObj = JSON.parseObject(shopQueryReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String lastChanged = extObj.getString("lastChanged");
            jsonObject.put("lastChanged", lastChanged);
        }

        JSONObject filterObj = new JSONObject();
        filterObj.put("isdel", 0);
        jsonObject.put("filter", filterObj);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "shop.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

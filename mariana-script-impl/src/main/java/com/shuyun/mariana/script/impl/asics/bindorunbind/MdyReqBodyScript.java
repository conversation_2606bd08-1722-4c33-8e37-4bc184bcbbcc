package com.shuyun.mariana.script.impl.asics.bindorunbind;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memunbind.MemberUnbindReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS会员解绑请求体转换脚本
 * 功能：将内部标准MemberUnbindReq协议转换为ASICS系统所需的字段格式
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberUnbindReq memberUnbindReq = JSON.parseObject(reqBodyInJson, MemberUnbindReq.class);
        JSONObject inputParam = JSON.parseObject(reqBodyInJson);
        
        //创建ASICS格式的输出对象
        JSONObject asicsObject = new JSONObject();
        
        //解析配置信息
        String appCfgJson = memberUnbindReq.getAppCfgJson();
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(appCfgJson)) {
            appCfgObj = JSON.parseObject(appCfgJson);
        }
        
        String bizExtJson = memberUnbindReq.getBizExtJson();
        JSONObject bizExtObj = null;
        if (StringUtils.isNotEmpty(bizExtJson)) {
            bizExtObj = JSON.parseObject(bizExtJson);
        }

        // 映射到ASICS字段格式 - 支持所有解绑相关字段
        
        // 基础信息字段
        if (StringUtils.isNotEmpty(memberUnbindReq.getUnionId())) {
            asicsObject.put("unionId", memberUnbindReq.getUnionId());
        }
        
        if (StringUtils.isNotEmpty(memberUnbindReq.getOpenId())) {
            asicsObject.put("wxShopOpenId", memberUnbindReq.getOpenId());
        }
        
//        if (StringUtils.isNotEmpty(memberUnbindReq.getNickName())) {
//            asicsObject.put("nickName", memberUnbindReq.getNickName());
//        }
        
        // 从输入参数或扩展字段中获取ASICS特有字段
        if (bizExtObj != null) {
            // ASICS特有字段
            putIfNotEmpty(asicsObject, "asOuid", bizExtObj.getString("asOuid"));
            putIfNotEmpty(asicsObject, "md5Nick", bizExtObj.getString("md5Nick"));
            putIfNotEmpty(asicsObject, "md5Phone", bizExtObj.getString("md5Phone"));
            putIfNotEmpty(asicsObject, "omid", bizExtObj.getString("omid"));
            putIfNotEmpty(asicsObject, "tmallOuid", bizExtObj.getString("tmallOuid"));
            //1:绑定 2:解绑
//            putIfNotEmpty(asicsObject, "type", bizExtObj.getString("type"));
            
            // channel 在后面统一处理
        }
        
        // 从输入参数中直接获取字段（优先级更高）
//        putIfNotEmpty(asicsObject, "asOuid", inputParam.getString("asOuid"));
//        putIfNotEmpty(asicsObject, "md5Nick", inputParam.getString("md5Nick"));
//        putIfNotEmpty(asicsObject, "md5Phone", inputParam.getString("md5Phone"));
//        putIfNotEmpty(asicsObject, "omid", inputParam.getString("omid"));
//        putIfNotEmpty(asicsObject, "tmallOuid", inputParam.getString("tmallOuid"));
//        putIfNotEmpty(asicsObject, "type", inputParam.getString("type"));
        
        // 渠道信息 - 使用基类方法设置
        setChannel(asicsObject, appCfgObj, inputParam);

        // 设置会员系统ID - 使用基类方法
        setMembershipSystemId(asicsObject, appCfgObj, inputParam);

        String result = asicsObject.toJSONString();
        return result;
    }
    
    /**
     * 辅助方法：如果值不为空则放入对象
     */
    private void putIfNotEmpty(JSONObject jsonObject, String key, String value) {
        if (StringUtils.isNotEmpty(value)) {
            jsonObject.put(key, value);
        }
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memUnbind.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("nickName", "糖x晓甜");
        object.put("asOuid", "ASICS_001");
        object.put("channel", 15);
        object.put("md5Nick", "md5_nick_hash");
        object.put("md5Phone", "md5_phone_hash");
        object.put("omid", "OMID_001");
        object.put("tmallOuid", "TMALL_001");
        object.put("type", "UNBIND");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\"}");
        object.put("bizExtJson","{\"asOuid\":\"ASICS_EXT_001\",\"type\":\"WECHAT_UNBIND\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

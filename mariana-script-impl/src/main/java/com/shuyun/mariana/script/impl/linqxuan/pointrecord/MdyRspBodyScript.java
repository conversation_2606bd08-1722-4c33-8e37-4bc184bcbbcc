package com.shuyun.mariana.script.impl.linqxuan.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordDto;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseLinQXuanScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap pageWrap = new PageWrap();
        List<PointRecordDto> pointRecordDtos = new ArrayList<>();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONArray itemArrObj = dataObj.getJSONArray("rows");
            if (Objects.nonNull(dataObj.getJSONObject("page"))) {
                pageWrap.setPageSize(dataObj.getJSONObject("page").getInteger("pageSize"));
                pageWrap.setTotalCount(dataObj.getJSONObject("page").getInteger("total"));;
                pageWrap.setPage(dataObj.getJSONObject("page").getInteger("page"));
            }
            if (Objects.nonNull(itemArrObj)) {
                for (int i = 0; i < itemArrObj.size(); i++) {

                    //组装流水
                    JSONObject pointRecordObj = itemArrObj.getJSONObject(i);

                    PointRecordDto pointRecordDto = new PointRecordDto();
                    pointRecordDto.setRecordType(pointRecordObj.getString("createType"));
                    pointRecordDto.setDescription(pointRecordObj.getString("context"));
                    pointRecordDto.setChangeTime(pointRecordObj.getString("addTime"));
                    pointRecordDto.setExpiredTime(pointRecordObj.getString("expireTime"));

                    //变更前积分
                    pointRecordDto.setCurPoint(pointRecordObj.getDouble("integral"));
                    pointRecordDto.setTotalPoint(pointRecordObj.getDouble("integralBefore"));
                    pointRecordDto.setPoint(pointRecordObj.getDouble("integral"));
                    pointRecordDtos.add(pointRecordDto);
                }
            }
        }

        pageWrap.setItems(pointRecordDtos);
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String recordRspStr = "{\n" +
                "    \"code\": 10000,\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"pageSize\": 1,\n" +
                "        \"totals\": 2,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"created\": \"2024-07-31 13:49:13\",\n" +
                "                \"changeType\": \"发放\",\n" +
                "                \"source\": \"SERVICE\",\n" +
                "                \"partnerSequence\": \"1722404953673491255\",\n" +
                "                \"changePoint\": 10,\n" +
                "                \"version\": \"1722404953673491255\",\n" +
                "                \"operator\": \"数云技术支持:meng.lv\",\n" +
                "                \"point\": 10,\n" +
                "                \"changeTime\": \"2024-07-31 13:49:13\",\n" +
                "                \"recordId\": null,\n" +
                "                \"platCode\": \"TESTCEM\",\n" +
                "                \"sequence\": \"1722404953673491255\",\n" +
                "                \"expired\": \"9999-12-31 23:59:59\",\n" +
                "                \"partner\": \"shuyun\",\n" +
                "                \"ouid\": null,\n" +
                "                \"shopId\": \"TESTCEM01\",\n" +
                "                \"id\": \"opmmb6YlYX2j54qtuGLUyCLvBVz1\",\n" +
                "                \"desc\": \"test增加1\",\n" +
                "                \"memberId\": 100852325800\n" +
                "            }\n" +
                "        ],\n" +
                "        \"httpCode\": true,\n" +
                "        \"pageNum\": 2\n" +
                "    }\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, recordRspStr);
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
//        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

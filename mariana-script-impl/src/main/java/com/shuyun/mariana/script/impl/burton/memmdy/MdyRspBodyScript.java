package com.shuyun.mariana.script.impl.burton.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyRsp;
import com.shuyun.mariana.script.impl.burton.BaseBurtonScriptBase;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=36
 */
public class MdyRspBodyScript extends BaseBurtonScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberMdyRsp memberMdyRsp = new MemberMdyRsp();
        RestWrap<MemberMdyRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.setSuccess(true);
        if (Objects.isNull(rspObj)) {
            regRspRestWrap.setSuccess(true);
        }else {
            regRspRestWrap.setSuccess(false);
            regRspRestWrap.setMessage(JSON.toJSONString(rspObj));
        }
        regRspRestWrap.setData(memberMdyRsp);
        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memMdy.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @IgnoreGen
    @Override
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("memberId", "DVF20240711WX00000");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.miele.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.miele.BaseMieleScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMieleScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        MemberDetailRsp memberDetailRsp = new MemberDetailRsp();
        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            memberDetailRsp = JSON.parseObject(JSON.toJSONString(dataObj), MemberDetailRsp.class);

            Integer marriageStatus = 2;
            //M:已婚 S:未婚 D:离异 O:其他
            String marrage = dataObj.getString("marriage");
            //0未婚1已婚2未知
            if (StringUtils.isEmpty(marrage)) {
                marriageStatus = 2;
            } else if (marrage.equals("O")) {
                marriageStatus = 2;
            } else if (marrage.equals("S")) {
                marriageStatus = 0;
            } else if (marrage.equals("M")) {
                marriageStatus = 1;
            }
            memberDetailRsp.setMarriageStatus(marriageStatus);
            String dateOfBirth = dataObj.getString("dateOfBirth");
            memberDetailRsp.setBirthday(dateOfBirth);
            memberDetailRsp.setMemberName(dataObj.getString("fullName"));
            memberDetailRsp.setNickname(dataObj.getString("nick"));
        }

        RestWrap<MemberDetailRsp> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(memberDetailRsp);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memDetail.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("memberId", "DVF20240711WX00000");
        dataObj.put("mobile", "18392119627");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

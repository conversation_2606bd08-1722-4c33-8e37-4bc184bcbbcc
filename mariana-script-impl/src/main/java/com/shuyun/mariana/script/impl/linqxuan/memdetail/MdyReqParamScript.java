package com.shuyun.mariana.script.impl.linqxuan.memdetail;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.memdetail.MemberDetailReq;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.linqxuan.BaseLinQXuanScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * https://open.shuyun.com/#/apidoc?type=41&apiId=242
 */
public class MdyReqParamScript extends BaseLinQXuanScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        MemberDetailReq memberDetailReq = JSON.parseObject(reqBodyInJson, MemberDetailReq.class);

        RequestHandle requestHandle = new RequestHandle();
        RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
        requestMethod.setHttpMethod(HttpMethod.GET.name());
        requestHandle.setRequestMethod(requestMethod);

        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();
        //过滤条件类型：1-手机号、  2-会员卡号、  3-外部卡号、  4-微信unionid，5-微信openid

        //默认openId就是vipCode
        addParameters.put("type", "2");
        addParameters.put("filterValue", memberDetailReq.getOpenId());
        addParameters.put("vipSeriesCode", "001");

        //基于查询方式 queryType：openId,mobile
        JSONObject extObj = JSON.parseObject(memberDetailReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String queryType = extObj.getString("queryType");
            if (StringUtils.isNotEmpty(queryType) && queryType.equals("mobile")) {
                addParameters.put("type", "1");
                addParameters.put("filterValue", memberDetailReq.getMobile());
            } else if (StringUtils.isNotEmpty(queryType) && queryType.equals("realOpenId")) {
                addParameters.put("type", "5");
                addParameters.put("filterValue", memberDetailReq.getOpenId());
            }
        }

        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memDetail.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("shopId", "wxf492c06764b16035");
        object.put("platCode", "WEIXIN");
        object.put("tenant", "tenant");
        object.put("unionId", "unionId");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

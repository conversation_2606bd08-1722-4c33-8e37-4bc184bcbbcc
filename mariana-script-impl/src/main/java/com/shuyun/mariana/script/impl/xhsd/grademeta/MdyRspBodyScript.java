package com.shuyun.mariana.script.impl.xhsd.grademeta;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseXhsdScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONArray dataObj = rspObj.getJSONArray("data");
        List<JSONObject> resultJsonArray = new ArrayList<>();
        if (dataObj!=null) {
            for (int i = 0; i < dataObj.size(); i++) {
                JSONObject jsonObj = dataObj.getJSONObject(i);
                JSONObject projectExt =new JSONObject();
                projectExt.put("upgradeRule",jsonObj.get("upgradeRule"));
                projectExt.put("stayGradeRule",jsonObj.get("stayGradeRule"));
                jsonObj.put("projectExt",JSON.toJSONString(projectExt));
                resultJsonArray.add(jsonObj);
            }
        }
        RestWrap<List<JSONObject>> regRspRestWrap = new RestWrap<>();
        regRspRestWrap.buildSuccess(resultJsonArray);

        String result = JSON.toJSONString(regRspRestWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".gradeMeta.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    public static void main(String[] args) {
        MdyRspBodyScript mdyRspBodyScript = new MdyRspBodyScript();
        mdyRspBodyScript.genRspBodyScript(true);

    }
}

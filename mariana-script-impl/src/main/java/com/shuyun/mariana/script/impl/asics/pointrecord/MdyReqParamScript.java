package com.shuyun.mariana.script.impl.asics.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 * ASICS积分流水查询请求参数转换脚本
 * 功能：设置积分流水查询请求的HTTP方法和参数，支持填充查询参数memberCode
 */
public class MdyReqParamScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化请求body
        JSONObject reqBodyObj = JSON.parseObject(reqBodyInJson);

        RequestHandle requestHandle = new RequestHandle();
        //增加查询参数
        RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
        Map<String, String> addParameters = new HashMap<>();

        // 会员标识参数 - 优先使用memberId作为memberCode
        String memberCode = reqBodyObj.getString("memberCode");
        addParameters.put("memberCode", memberCode);

        requestParameter.setAddParameters(addParameters);
        requestHandle.setParameter(requestParameter);

        String result = JSON.toJSONString(requestHandle);
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".pointRecord.reqParam";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqParam;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberId", "ASICS_MEMBER_001");
        object.put("memberCode", "ASICS_CODE_001");
        object.put("startDate", "2024-01-01");
        object.put("endDate", "2024-12-31");
        object.put("page", "0");
        object.put("pageSize", "20");
        object.put("size", "20");
        object.put("sort", "0");
        object.put("startTime", "2024-01-01 00:00:00");
        object.put("endTime", "2024-12-31 23:59:59");
        object.put("appCfgJson","{\"channel\":\"15\",\"programCode\":\"ASICS001\",\"membershipSystemId\":\"1\",\"recordTypes\":\"SEND,DEDUCT,EXPIRE\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqParamScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

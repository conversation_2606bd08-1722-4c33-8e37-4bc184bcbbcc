package com.shuyun.mariana.script.impl.xhsd.memorder;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.xhsd.BaseXhsdScriptBase;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseXhsdScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        return rspObj.toJSONString();
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memOrder.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("shopTypeCode", "taobao");

        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);

        JSONObject pageObj = new JSONObject();
        pageObj.put("page", 0);
        pageObj.put("pageSize", 20);
        pageObj.put("totalCount", 2);
        pageObj.put("items", itemArr);

        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        object.put("data", pageObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.ccmsv2.memdetail;

import com.alibaba.fastjson.JSONObject;
import com.shuyun.mariana.script.impl.ccmsv2.BaseCcmsV2ScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsV2ScriptBase {
    @Override
    public String doRun(JSONObject rspObj) {
        return buildMemRsp(rspObj);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memDetail.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"gradeId\": 1100,\n" +
                "        \"gradeSort\": 1,\n" +
                "        \"gradeName\": \"店铺客户\",\n" +
                "        \"mobile\": \"***********\",\n" +
                "        \"gradeEffectiveTime\": \"2025-04-14 22:40:38\",\n" +
                "        \"gradeExpired\": \"\",\n" +
                "        \"memberId\": ************,\n" +
                "        \"point\": 0,\n" +
                "        \"growth\": 0,\n" +
                "        \"platAccounts\": [\n" +
                "            {\n" +
                "                \"gender\": \"F\",\n" +
                "                \"joinTime\": \"2025-04-14 22:40:38\",\n" +
                "                \"birthday\": \"\",\n" +
                "                \"account\": \"tz平台账号1\",\n" +
                "                \"name\": \"tzplatName\",\n" +
                "                \"platCode\": \"WEIXIN\",\n" +
                "                \"shopId\": \"wxaada9391b766b633\",\n" +
                "                \"sourceType\": \"SERVICE\",\n" +
                "                \"bindStatus\": true\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"message\": null\n" +
                "}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

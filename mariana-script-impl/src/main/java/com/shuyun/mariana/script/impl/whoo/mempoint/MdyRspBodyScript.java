package com.shuyun.mariana.script.impl.whoo.mempoint;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.whoo.BaseWhooScriptBase;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseWhooScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject memberPointQueryRsp = new JSONObject();
        Double point = rspObj.getDouble("point");
        if (Objects.nonNull(point)) {
            memberPointQueryRsp.put("pointDecimal", point);
            memberPointQueryRsp.put("point", point.intValue());
        } else {
            memberPointQueryRsp.put("pointDecimal", 0);
            memberPointQueryRsp.put("point", 0);
            point = 0.0;
        }

        JSONObject proExtObj = new JSONObject();
        Double expiringPoint = rspObj.getDouble("expiringPoint");
        if (Objects.nonNull(expiringPoint)) {
            proExtObj.put("expiringPointCurMonth", expiringPoint);
        } else {
            proExtObj.put("expiringPointCurMonth", 0);
        }

        Double expiringPoint7 = rspObj.getDouble("expiringPoint7");
        if (Objects.nonNull(expiringPoint7)) {
            proExtObj.put("expiringPoint7", expiringPoint7);
        } else {
            proExtObj.put("expiringPoint7", 0);
        }

        Double expiringPoint90 = rspObj.getDouble("expiringPoint90");
        if (Objects.nonNull(expiringPoint90)) {
            proExtObj.put("expiringPoint90", expiringPoint90);
        } else {
            proExtObj.put("expiringPoint90", 0);
        }

        Double shortTermPoint = rspObj.getDouble("shortTermPoint");
        proExtObj.put("shortTermPoint", 0);
        if (Objects.nonNull(shortTermPoint)) {
            proExtObj.put("shortTermPoint", shortTermPoint);
        } else {
            proExtObj.put("shortTermPoint", 0);
        }

        //那就是 可用积分 永远是可用积分，但是 活动积分 要取  min（可用积分，活动积分）from tangming
        Double shortTerm = proExtObj.getDouble("shortTermPoint");
        //可用积分比活动积分小，用可用积分
        if (point.compareTo(shortTerm) == -1) {
            proExtObj.put("shortTermPoint", point);
        }

        String projectExt = proExtObj.toJSONString();
        memberPointQueryRsp.put("projectExt", projectExt);

        RestWrap<JSONObject> restWrap = new RestWrap<>();
        restWrap.buildSuccess(memberPointQueryRsp);

        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "memPoint.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "{\"point\":2180.0,\"code\":\"0\",\"message\":\"success\"}");
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

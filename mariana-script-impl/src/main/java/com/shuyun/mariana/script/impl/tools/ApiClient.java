package com.shuyun.mariana.script.impl.tools;

import com.alibaba.fastjson.JSON;
import com.shuyun.platform.common.utils.http.OkHttpUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/15
 * @Version 1.0
 **/
@Slf4j
public class ApiClient {

    public static void uploadScript(ScriptPO scriptPO) {
        String json = JSON.toJSONString(scriptPO);
        String result = OkHttpUtils.httpPostJson("https://qa-ual.shuyun.com/mariana-admin/v1/script/saveOrUpdate", null, json, false);
        System.out.println("upload script:" +  json + ",result is:" + result);
    }
}

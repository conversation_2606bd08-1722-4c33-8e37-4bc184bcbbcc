package com.shuyun.mariana.script.impl.kylin_product.memgrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memgrade.MemberGradeQueryRsp;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseKylinProScriptBase {

    @Override
    public String run(String rspBodyInJson) {
        RestWrap<MemberGradeQueryRsp> restWrap = new RestWrap<>();

        JSONArray gradeArr = JSON.parseArray(rspBodyInJson);
        if (Objects.nonNull(gradeArr) && gradeArr.size() > 0) {
            JSONObject gradeObj = gradeArr.getJSONObject(0);
            MemberGradeQueryRsp memberGradeQueryRsp = new MemberGradeQueryRsp();
            memberGradeQueryRsp.setGradeId(gradeObj.getString("currentGradeDefinitionId"));
//            memberGradeQueryRsp.setGradeSort(gradeObj.getInteger("currentGradeDefinitionId"));
            memberGradeQueryRsp.setGradeName(gradeObj.getString("gradeDefinitionName"));
            memberGradeQueryRsp.setEffectTime(gradeObj.getString("effectDate"));
            //没有过期时间就是永久有效
            memberGradeQueryRsp.setExpiredTime(gradeObj.getString("overdueDate"));
            restWrap.buildSuccess(memberGradeQueryRsp);
        }

        return JSON.toJSONString(restWrap);
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memGrade.rspBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("success", true);
        object.put("code", "testCode");
        object.put("message", "testErrMsg");

        JSONObject dataObj = new JSONObject();
        dataObj.put("memberId", "DVF20240709WX00003");
        dataObj.put("id", 60018);
        dataObj.put("name", "积分卡");
        dataObj.put("effectTime", "2024-07-09 18:14:01");
        object.put("data", dataObj);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, "[\n" +
                "    {\n" +
                "        \"planId\": 60001,\n" +
                "        \"planName\": \"后忠诚度计划\",\n" +
                "        \"subjectId\": 60002,\n" +
                "        \"subjectName\": \"后会员\",\n" +
                "        \"subjectFqn\": \"data.mrm.member.whoo.Member\",\n" +
                "        \"gradeHierarchyId\": 60005,\n" +
                "        \"gradeHierarchyName\": \"后等级体系\",\n" +
                "        \"memberId\": \"WHOOZZJSXU\",\n" +
                "        \"currentGradeDefinitionId\": 60006,\n" +
                "        \"gradeDefinitionName\": \"粉丝\",\n" +
                "        \"effectDate\": \"2024-12-10T17:03:16.395+08:00\"\n" +
                "    }\n" +
                "]");
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        GenScriptParam genScriptParam = new GenScriptParam();
        genScriptParam.setUploadToApiPlat(true);
        genScriptParam.addPExcludeFunc("run");
        genScriptParam.addIncludeFunc("run", false);
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScriptExtend(genScriptParam);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

//package com.shuyun.mariana.script.impl.tools;
//
//import com.google.common.collect.Sets;
//import com.shuyun.mariana.script.impl.protocol.BaseScriptIfc;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//
//import java.io.File;
//import java.io.IOException;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//
///**
// * @Description
// * <AUTHOR>
// * @Date 2024/7/14
// * @Version 1.0
// **/
//@Slf4j
//public class ScriptGenerator {
//
//    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
//        String reqBodyClassName = "com.shuyun.mariana.script.impl.dvf.memorder.MdyReqBodyScript";
//        genReqBodyScript(reqBodyClassName, false);
//
//        String rspBodyClassName = "com.shuyun.mariana.script.impl.dvf.memorder.MdyRspBodyScript";
////        genRspBodyScript(rspBodyClassName, true);
//    }
//
//
//    private static void genReqBodyScript(String clazzName, Boolean uploadToApiPlat) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
//        //1 描述源文件完整路径
//        String dir = "C:\\code\\cbb\\gateway\\mariana-script-family\\mariana-script-impl\\src\\main\\java";
//
//        //2 获取类 import语句和run语句
//        Class clazz = Class.forName(clazzName);
//        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, clazz, Sets.newHashSet("run"), Sets.newHashSet(), false);
//
//        //3 获取父类 import语句 和 function
//        Class pClazz = clazz.getSuperclass();
//        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, pClazz, Sets.newHashSet(), Sets.newHashSet("run", "doRun"), true);
//
//        //4 组装并生成文件
//        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
//        BaseScriptIfc baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
//        genScript(scriptContent, baseScriptIfc);
//
//        //5 上传api平台
//        if (uploadToApiPlat) {
//            ScriptPO scriptPO = new ScriptPO();
//            scriptPO.setName(baseScriptIfc.groovyFileName());
//            scriptPO.setBizType(baseScriptIfc.bizType());
//            scriptPO.setScriptContent(scriptContent);
//            ApiClient.uploadScript(scriptPO);
//        }
//    }
//
//    private static void genRspBodyScript(String clazzName, Boolean uploadToApiPlat) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
//        //1 描述源文件完整路径
//        String dir = "C:\\code\\cbb\\gateway\\mariana-script-family\\mariana-script-impl\\src\\main\\java";
//
//        //2 获取父类 import语句和run语句
//        Class clazz = Class.forName(clazzName);
//        Class pClazz = clazz.getSuperclass();
//        ParseResultDto parseResultDto = CodeParser.fetchScriptContent(dir, pClazz, Sets.newHashSet(), Sets.newHashSet("doRun", "buildCommon"), false);
//
//        //3 获取子类 import语句 和 function
//        ParseResultDto pParseResultDto = CodeParser.fetchScriptContent(dir, clazz, Sets.newHashSet("doRun"), Sets.newHashSet(), true);
//
//        //4 组装并生成文件
//        String scriptContent = buildScriptContent(parseResultDto, pParseResultDto);
//        BaseScriptIfc baseScriptIfc = (BaseScriptIfc) clazz.newInstance();
//        genScript(scriptContent, baseScriptIfc);
//
//        //5 上传api平台
//        if (uploadToApiPlat) {
//            ScriptPO scriptPO = new ScriptPO();
//            scriptPO.setName(baseScriptIfc.groovyFileName());
//            scriptPO.setBizType(baseScriptIfc.bizType());
//            scriptPO.setScriptContent(scriptContent);
//            ApiClient.uploadScript(scriptPO);
//        }
//    }
//
//    private static String buildScriptContent(ParseResultDto parseResultDto, ParseResultDto pParseResultDto) {
//        StringBuilder content = new StringBuilder("");
//        if (StringUtils.isNotEmpty(parseResultDto.getImportStr())) {
//            content.append(parseResultDto.getImportStr());
//            content.append("\n");
//        }
//
//        if (StringUtils.isNotEmpty(pParseResultDto.getImportStr())) {
//            content.append(pParseResultDto.getImportStr());
//            content.append("\n");
//        }
//
//        if (StringUtils.isNotEmpty(parseResultDto.getFuncStr())) {
//            content.append(parseResultDto.getFuncStr());
//            content.append("\n");
//        }
//
//        if (StringUtils.isNotEmpty(pParseResultDto.getFuncStr())) {
//            content.append(pParseResultDto.getFuncStr());
//            content.append("\n");
//        }
//
//        return content.toString();
//    }
//
//    public static void genScript(String scriptContent, BaseScriptIfc baseScriptIfc) {
//        //指定目录，创建文件
//        String dir = System.getProperty("user.dir");
//        String path = dir + "\\mariana-script-impl\\src\\main\\resources";
//        String realPath = path;
//        String fileName = baseScriptIfc.groovyFileName();
//        //todo 拼装日期
//        String filePath = realPath + "\\" + fileName;
//        File file = new File(filePath);
//        try {
//            file.createNewFile();
//        } catch (IOException e) {
//            log.error(e.getMessage(), e);
//        }
//
//        //写入文件
//        try {
//            Files.write(Paths.get(path, fileName), scriptContent.getBytes());
//        } catch (IOException e) {
//            log.error(e.getMessage(), e);
//        }
//
//        log.info("gen script success, fileName:{}, path:{}.", fileName, filePath);
//    }
//
//    public static void genScriptAndUploadToAPIPlat() {
//
//    }
//}

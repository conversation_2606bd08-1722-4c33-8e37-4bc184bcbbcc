package com.shuyun.mariana.script.impl.meilv.memorder;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.meilv.BaseMeiLvScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseMeiLvScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        JSONObject resultObj=new JSONObject();
        JSONArray data = rspObj.getJSONArray("data");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
        SimpleDateFormat dateFormat2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (data!=null){
            for (int i = 0; i < data.size(); i++) {
                try {
                    JSONObject jsonObj = data.getJSONObject(i);
                    String orderTime = jsonObj.getString("orderTime");
                    if (orderTime!=null){
                        Date orderTimeDate = dateFormat.parse(orderTime);
                        String orderTimeStr = dateFormat2.format(orderTimeDate);
                        jsonObj.put("orderTime",orderTimeStr);
                    }
                    String payTime = jsonObj.getString("payTime");
                    if (payTime!=null){
                        Date payTimeDate = dateFormat.parse(payTime);
                        String payTimeStr = dateFormat2.format(payTimeDate);
                        jsonObj.put("payTime",payTimeStr);
                    }
                    JSONArray orderItems = jsonObj.getJSONArray("orderItems");
                    if (orderItems!=null){
                        for (int j = 0; j < orderItems.size(); j++){
                            JSONObject jsonObjItems = orderItems.getJSONObject(j);
                            String orderItemTime = jsonObjItems.getString("orderTime");
                            if (orderItemTime!=null){
                                Date orderItemTimeDate = dateFormat.parse(orderItemTime);
                                String orderItemTimeStr = dateFormat2.format(orderItemTimeDate);
                                jsonObjItems.put("orderTime",orderItemTimeStr);
                            }
                            String finishTime = jsonObjItems.getString("finishTime");
                            if (finishTime!=null){
                                Date finishTimeDate = dateFormat.parse(finishTime);
                                String finishTimeStr = dateFormat2.format(finishTimeDate);
                                jsonObjItems.put("finishTime",finishTimeStr);
                            }
                        }
                        jsonObj.put("orderItems",orderItems);
                    }
                }catch (Exception e){

                }
            }
        }
        resultObj.put("items",data);
        resultObj.put("totalPage",rspObj.getInteger("totalPage"));
        resultObj.put("totalCount",rspObj.getInteger("totalCount"));
        resultObj.put("page",rspObj.getInteger("currentPage"));
        resultObj.put("pageSize",rspObj.getInteger("pageSize"));
        RestWrap<Map<String,Object>> restWrap = new RestWrap<>();
        restWrap.buildSuccess(resultObj);
        String result = JSON.toJSONString(restWrap);
        return result;
    }

    @Override
    public String groovyFileName() {
        return projectDir()+".memOrder.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject itemObj = new JSONObject();
        itemObj.put("shopTypeCode", "taobao");

        JSONArray itemArr = new JSONArray();
        itemArr.add(itemObj);

        JSONObject pageObj = new JSONObject();
        pageObj.put("currentPage", 1);
        pageObj.put("pageSize", 20);
        pageObj.put("totalCount", 2);
        pageObj.put("data", itemArr);

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, pageObj.toJSONString());
        return param;
    }

    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

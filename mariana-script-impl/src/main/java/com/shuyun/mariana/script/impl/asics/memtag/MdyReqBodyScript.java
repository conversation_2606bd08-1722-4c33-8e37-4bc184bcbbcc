package com.shuyun.mariana.script.impl.asics.memtag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.cem.std.member.protocol.memtag.querymemtag.MemTagQueryReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import com.shuyun.mariana.script.impl.asics.BaseAsicsScriptBase;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseAsicsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //鍙嶅簭鍒楀寲涓哄崗璁?
        JSONObject memTagQueryReq = JSON.parseObject(reqBodyInJson);
        //鏋勫缓閫氱敤
        JSONObject jsonObject = new JSONObject();

        //鍙嶅簭鍒楀寲 ext_cfg锛屽緱鍒板崱璁″垝ID
        String appCfgJson = memTagQueryReq.getString("appCfgJson");
        if (StringUtils.isNotEmpty(appCfgJson)) {
            JSONObject appCfgObj = JSON.parseObject(appCfgJson);
            jsonObject.put("programCode", appCfgObj.getString("programCode"));
            jsonObject.put("channelType", appCfgObj.getString("channel"));
        }

        jsonObject.put("memberId", memTagQueryReq.getString("memberId"));
        jsonObject.put("tagIds", memTagQueryReq.getJSONArray("tagIds"));
        jsonObject.put("page", memTagQueryReq.getIntValue("page"));
        jsonObject.put("pageSize", memTagQueryReq.getIntValue("pageSize"));

        // 设置渠道信息和会员系统ID - 使用基类方法
        JSONObject appCfgObj = null;
        if (StringUtils.isNotEmpty(memTagQueryReq.getString("appCfgJson"))) {
            appCfgObj = JSON.parseObject(memTagQueryReq.getString("appCfgJson"));
        }
        setChannel(jsonObject, appCfgObj, memTagQueryReq);
        setMembershipSystemId(jsonObject, appCfgObj, memTagQueryReq);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + ".memTag.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("bizExtJson","{\"gender\":\"M\",\"registerGuide\":\"A99999\",\"registerGuideName\":\"鏁颁簯涓氬姟閮?涓戒附\",\"registerShopCode\":\"SHOP002\",\"registerShopName\":\"SHOP002\"}");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //鐢熸垚鑴氭湰
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //娴嬭瘯鑴氭湰
        absScriptGenerate.testScript(scriptContext);
    }
}


package com.shuyun.mariana.script.impl.ccms.memreg;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 *
 * https://open.shuyun.com/#/apidoc?type=41&apiId=40
 */
public class MdyReqBodyScript extends BaseCcmsScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberRegReq memberRegReq = JSON.parseObject(reqBody<PERSON>n<PERSON><PERSON>, MemberRegReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberRegReq);

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberRegReq);

        //填充ID和 unnionId
        //平台客户账号
        jsonObject.put("id", memberRegReq.getOpenId());
        jsonObject.put("omid", memberRegReq.getUnionId());

        //反序列化扩展字段
        JSONObject extObj = JSON.parseObject(memberRegReq.getBizExtJson());
        if (Objects.nonNull(extObj)) {
            String gender = extObj.getString("gender");
            jsonObject.put("gender", gender);
        }

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memReg.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

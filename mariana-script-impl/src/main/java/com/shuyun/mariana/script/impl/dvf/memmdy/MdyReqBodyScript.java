package com.shuyun.mariana.script.impl.dvf.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseDvfScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);
        //构建通用
        JSONObject jsonObject = buildCommon(memberMdyReq);

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberMdyReq);
        String gender=jsonObject.getString("gender");
        JSONObject extObj = JSON.parseObject(memberMdyReq.getBizExtJson());
        if (gender==null || "".equalsIgnoreCase(gender)){
            //反序列化扩展字段
            if (Objects.nonNull(extObj)) {
                String genderStr = extObj.getString("gender");
                jsonObject.put("gender", genderStr);
            }
        }
        JSONObject extras=new JSONObject();
        if (Objects.nonNull(extObj)) {
            String professionStr = extObj.getString("profession");
            if (professionStr!=null&&!"".equalsIgnoreCase(professionStr)){
                extras.put("profession", professionStr);
            }
            String colorStr = extObj.getString("color");
            if (colorStr!=null&&!"".equalsIgnoreCase(colorStr)){
                extras.put("color", colorStr);
            }
            String placeStr = extObj.getString("place");
            if (placeStr!=null&&!"".equalsIgnoreCase(placeStr)){
                extras.put("place", placeStr);
            }
            String revenueStr = extObj.getString("revenue");
            if (revenueStr!=null&&!"".equalsIgnoreCase(revenueStr)){
                extras.put("revenue", revenueStr);
            }
            String emailStr = extObj.getString("email");
            if (emailStr!=null&&!"".equalsIgnoreCase(emailStr)){
                extras.put("email", emailStr);
            }



            String purchaseTimesInforStr = extObj.getString("purchaseTimesInfor");
            if (purchaseTimesInforStr!=null&&!"".equalsIgnoreCase(purchaseTimesInforStr)){
                extras.put("purchaseTimesInfor", purchaseTimesInforStr);
            }


            String acceptSourceInforStr = extObj.getString("acceptSourceInfor");
            if (acceptSourceInforStr!=null&&!"".equalsIgnoreCase(acceptSourceInforStr)){
                extras.put("acceptSourceInfor", acceptSourceInforStr);
            }


            String followFactorInforStr = extObj.getString("followFactorInfor");
            if (followFactorInforStr!=null&&!"".equalsIgnoreCase(followFactorInforStr)){
                extras.put("followFactorInfor", followFactorInforStr);
            }


            String preferBrandInforStr = extObj.getString("preferBrandInfor");
            if (preferBrandInforStr!=null&&!"".equalsIgnoreCase(preferBrandInforStr)){
                extras.put("preferBrandInfor", preferBrandInforStr);
            }



            String satisfactionInforStr = extObj.getString("satisfactionInfor");
            if (satisfactionInforStr!=null&&!"".equalsIgnoreCase(satisfactionInforStr)){
                extras.put("satisfactionInfor", satisfactionInforStr);
            }

        }
        jsonObject.put("extras",extras);
        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return "dvf.memMdy.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("nickname", "test1");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

package com.shuyun.mariana.script.impl.ccms.pointrecord;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.PageWrap;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memorder.OrderDto;
import com.shuyun.cem.std.member.protocol.memorder.OrderItemDto;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordDto;
import com.shuyun.cem.std.member.protocol.pointrecord.PointRecordTypeEnum;
import com.shuyun.mariana.script.impl.ccms.BaseCcmsScriptBase;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.dvf.BaseDvfScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.GenScriptParam;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyRspBodyScript extends BaseCcmsScriptBase {

    @Override
    public String doRun(JSONObject rspObj) {
        PageWrap pageWrap = new PageWrap();
        List<PointRecordDto> pointRecordDtos = new ArrayList<>();

        JSONObject dataObj = rspObj.getJSONObject("data");
        if (Objects.nonNull(dataObj)) {
            JSONArray itemArrObj = dataObj.getJSONArray("list");
            pageWrap.setPageSize(dataObj.getInteger("pageSize"));
            pageWrap.setTotalCount(dataObj.getInteger("totals"));;
            pageWrap.setPage(dataObj.getInteger("pageNum"));
            if (Objects.nonNull(itemArrObj)) {
                for (int i = 0; i < itemArrObj.size(); i++) {

                    //组装流水
                    JSONObject pointRecordObj = itemArrObj.getJSONObject(i);

                    PointRecordDto pointRecordDto = new PointRecordDto();
                    pointRecordDto.setMemberId(pointRecordObj.getString("memberId"));
                    pointRecordDto.setId(pointRecordObj.getString("sequence"));

                    //变更前积分
                    Double changePoint = pointRecordObj.getDouble("changePoint");
                    Double point = pointRecordObj.getDouble("point");
                    pointRecordDto.setCurPoint(point);

                    //流水类型  积分变更类型,枚举值： 发放、消费、作废、冻结、解冻、过期
                    String changeType = pointRecordObj.getString("changeType");
                    String recordType = null;
                    Double totalPoint = new Double(0);
                    if ("发放".equals(changeType)) {
                        recordType = PointRecordTypeEnum.SEND.name();
                        totalPoint = point - changePoint;
                    } else if ("消费".equals(changeType)) {
                        recordType = PointRecordTypeEnum.CONSUME.name();
                        totalPoint = point - changePoint;
                    } else if ("作废".equals(changeType)) {
                        recordType = PointRecordTypeEnum.ABOLISH.name();
                        totalPoint = point - changePoint;
                    } else if ("冻结".equals(changeType)) {
                        recordType = PointRecordTypeEnum.FREEZE.name();
                        totalPoint = point - changePoint;
                    } else if ("解冻".equals(changeType)) {
                        recordType = PointRecordTypeEnum.UNFREEZE.name();
                        totalPoint = point + changePoint;
                    } else if ("过期".equals(changeType)) {
                        recordType = PointRecordTypeEnum.EXPIRE.name();
                        totalPoint = point - changePoint;
                    }
                    pointRecordDto.setRecordType(changeType);
                    pointRecordDto.setTotalPoint(totalPoint);
                    pointRecordDto.setDescription(pointRecordObj.getString("desc"));
//                    pointRecordDto.setChannelType(pointRecordObj.getString("source"));
                    pointRecordDto.setShopCode(pointRecordObj.getString("shopId"));
                    pointRecordDto.setPoint(changePoint);
                    pointRecordDto.setChangeTime(pointRecordObj.getString("changeTime"));
                    pointRecordDto.setExpiredTime(pointRecordObj.getString("expired"));
                    pointRecordDto.setRecordSourceDetail(pointRecordObj.getString("source"));

                    pointRecordDtos.add(pointRecordDto);
                }
            }
        }

        pageWrap.setItems(pointRecordDtos);
        RestWrap restWrap = new RestWrap();
        restWrap.buildSuccess(pageWrap);
        return JSON.toJSONString(restWrap);
    }

    @Override
    public String groovyFileName() {
        return projectDir() + "." + "pointRecord.rspBody";
    }

    @Override
    public String bizType() {
        return ConstantKey.ScriptBizType.rspBody;
    }

    @Override
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        String recordRspStr = "{\n" +
                "    \"code\": 10000,\n" +
                "    \"success\": true,\n" +
                "    \"data\": {\n" +
                "        \"pageSize\": 1,\n" +
                "        \"totals\": 2,\n" +
                "        \"list\": [\n" +
                "            {\n" +
                "                \"created\": \"2024-07-31 13:49:13\",\n" +
                "                \"changeType\": \"发放\",\n" +
                "                \"source\": \"SERVICE\",\n" +
                "                \"partnerSequence\": \"1722404953673491255\",\n" +
                "                \"changePoint\": 10,\n" +
                "                \"version\": \"1722404953673491255\",\n" +
                "                \"operator\": \"数云技术支持:meng.lv\",\n" +
                "                \"point\": 10,\n" +
                "                \"changeTime\": \"2024-07-31 13:49:13\",\n" +
                "                \"recordId\": null,\n" +
                "                \"platCode\": \"TESTCEM\",\n" +
                "                \"sequence\": \"1722404953673491255\",\n" +
                "                \"expired\": \"9999-12-31 23:59:59\",\n" +
                "                \"partner\": \"shuyun\",\n" +
                "                \"ouid\": null,\n" +
                "                \"shopId\": \"TESTCEM01\",\n" +
                "                \"id\": \"opmmb6YlYX2j54qtuGLUyCLvBVz1\",\n" +
                "                \"desc\": \"test增加1\",\n" +
                "                \"memberId\": 100852325800\n" +
                "            }\n" +
                "        ],\n" +
                "        \"httpCode\": true,\n" +
                "        \"pageNum\": 2\n" +
                "    }\n" +
                "}";

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.RSP_BODY_EXTEND, recordRspStr);
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
//        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

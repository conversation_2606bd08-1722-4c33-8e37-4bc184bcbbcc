package com.shuyun.mariana.script.impl.kylin_product.memmdy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memmdy.MemberMdyReq;
import com.shuyun.mariana.script.impl.config.ConstantKey;
import com.shuyun.mariana.script.impl.kylin_product.BaseKylinProScriptBase;
import com.shuyun.mariana.script.impl.protocol.AbsScriptGenerate;
import com.shuyun.mariana.script.impl.protocol.IgnoreGen;
import com.shuyun.mariana.script.impl.protocol.ScriptContext;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: meng.lv
 * @Date: 2024/7/12 11:14
 */
public class MdyReqBodyScript extends BaseKylinProScriptBase {

    @Override
    public String run(String reqBodyInJson) {
        //反序列化为协议
        MemberMdyReq memberMdyReq = JSON.parseObject(reqBodyInJson, MemberMdyReq.class);

        JSONObject inputObj = JSON.parseObject(reqBodyInJson);
        //构建通用
        JSONObject jsonObject = buildCommon(memberMdyReq);

        //会员基础业务数据填充
        buildMemberBiz(jsonObject, memberMdyReq);
        String memberId = inputObj.getString("memberId");
        if (StringUtils.isNotEmpty(memberId)) {
            jsonObject.put("memberId", memberId);
        }

        JSONObject filter = new JSONObject();
        filter.put("customerNo", memberMdyReq.getAppId() + "_" + memberMdyReq.getOpenId());
        jsonObject.put("filter", filter);

        //param 对象
        JSONObject param = new JSONObject();
        if (StringUtils.isNotEmpty(memberMdyReq.getHeadImgUrl())) {
            param.put("headImgUrl", memberMdyReq.getHeadImgUrl());
        }
        if (StringUtils.isNotEmpty(memberMdyReq.getMemberName())) {
            param.put("fullName", memberMdyReq.getMemberName());
        }
        if (StringUtils.isNotEmpty(memberMdyReq.getGender())) {
            param.put("gender", memberMdyReq.getGender());
        }
        //不支持修改生日
//        if (StringUtils.isNotEmpty(memberMdyReq.getBirthday())) {
//            param.put("dateOfBirth", memberMdyReq.getBirthday());
//        }

        //extras
        String appCfgJson = memberMdyReq.getAppCfgJson();
        JSONObject appCfgObj = JSON.parseObject(appCfgJson);
        String bizExtJson = memberMdyReq.getBizExtJson();
        if (StringUtils.isNotEmpty(bizExtJson)) {
            JSONObject bizExtObj = JSON.parseObject(bizExtJson);
            JSONArray optJsonArr = appCfgObj.getJSONArray("optionalFields");
            if (Objects.nonNull(optJsonArr)) {
                JSONObject extras = new JSONObject();
                List<String> optFieldArr = optJsonArr.toJavaList(String.class);
                for (String optField : optFieldArr) {
                    if (StringUtils.isNotEmpty(bizExtObj.getString(optField))) {
                        extras.put(optField, bizExtObj.getString(optField));
                    }
                }
                jsonObject.put("extras", extras);
            }
        }

        jsonObject.put("params", param);

        String result = jsonObject.toJSONString();
        return result;
    }

    @Override
    @IgnoreGen
    public String groovyFileName() {
        return projectDir() + "." + "memMdy.reqBody";
    }

    @Override
    @IgnoreGen
    public String bizType() {
        return ConstantKey.ScriptBizType.reqBody;
    }

    @Override
    @IgnoreGen
    public String apiCode() {
        return null;
    }

    @Override
    @IgnoreGen
    public Map<String, Object> scriptInputParam() {
        JSONObject object = new JSONObject();
        object.put("appId", "wxf492c06764b16035");
        object.put("openId", "op0fW5C2GgH1Jc-Jlp2B6yEFjoQQ");
        object.put("unionId", "obm3r0ZqpCQ_Oe3zi7SwQltfzQKA");
        object.put("mobile", "18710426216");
        object.put("memberName", "test1");

        Map<String, Object> param = new HashMap<>();
        param.put(ConstantKey.REQ_BODY_EXTEND, object.toJSONString());
        return param;
    }

    @IgnoreGen
    public static void main(String[] args) throws ClassNotFoundException, IllegalAccessException, InstantiationException {
        AbsScriptGenerate absScriptGenerate = buildInstance();
        //生成脚本
        ScriptContext scriptContext = absScriptGenerate.genReqBodyScript(true);
        //测试脚本
        absScriptGenerate.testScript(scriptContext);
    }
}

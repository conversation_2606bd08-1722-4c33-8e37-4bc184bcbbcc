import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegRsp;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.MemberTaggingRsp;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

JSONObject rspObj = JSON.parseObject(rspBodyInJson);
Boolean success = rspObj.getBoolean("success");
if (!success) {
    RestWrap errRestWrap = new RestWrap();
    errRestWrap.setSuccess(false);
    errRestWrap.setCode(rspObj.getString("code"));
    errRestWrap.setMessage(rspObj.getString("message"));
    return JSON.toJSONString(errRestWrap);
}
return doRun(rspObj);


public String doRun(JSONObject rspObj) {
    MemberTaggingRsp memberTaggingRsp = new MemberTaggingRsp();
    JSONObject dataObj = rspObj.getJSONObject("data");
    if (Objects.nonNull(dataObj)) {
        memberTaggingRsp.setStatus("success");
    }
    RestWrap<MemberTaggingRsp> restWrap = new RestWrap<>();
    restWrap.buildSuccess(memberTaggingRsp);
    String result = JSON.toJSONString(restWrap);
    return result;
}

public String groovyFileName() {
    return projectDir() + "." + "memTagging.rspBody";
}

public String bizType() {
    return ConstantKey.ScriptBizType.rspBody;
}

public String apiCode() {
    return null;
}

public Map<String, Object> scriptInputParam() {
    JSONObject object = new JSONObject();
    object.put("success", true);
    object.put("code", "testCode");
    object.put("message", "testErrMsg");
    JSONObject dataObj = new JSONObject();
    dataObj.put("status", "REGISTERED");
    object.put("data", dataObj);
    Map<String, Object> param = new HashMap<>();
    param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
    return param;
}
public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
    AbsScriptGenerate absScriptGenerate = buildInstance();
    // 生成脚本
    ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
    // 测试脚本
    absScriptGenerate.testScript(scriptContext);
}


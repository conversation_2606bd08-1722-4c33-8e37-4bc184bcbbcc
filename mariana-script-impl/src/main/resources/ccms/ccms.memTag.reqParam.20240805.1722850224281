import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import org.springframework.http.HttpMethod;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import java.util.Objects;

RequestHandle requestHandle = new RequestHandle();
RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
requestMethod.setHttpMethod(HttpMethod.GET.name());
requestHandle.setRequestMethod(requestMethod);
RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
Map<String, String> addParameters = JSON.parseObject(reqBodyInJson, Map.class);
addParameters.put("platAccount", addParameters.get("unionId"));
requestParameter.setAddParameters(addParameters);
requestHandle.setParameter(requestParameter);
String result = JSON.toJSONString(requestHandle);
return result;

public JSONObject buildCommon(BaseMember baseMember) {
    // copy通用变量
    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
    return jsonObject;
}
public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
    jsonObject.put("name", memberRegReq.getMemberName());
    jsonObject.put("guideId", memberRegReq.getGuideCode());
    // 平台客户账号
    jsonObject.put("id", memberRegReq.getUnionId());
}


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.MemberTaggingReq;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.TaggingDto;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import java.util.Objects;

// 反序列化为协议
MemberTaggingReq memberTaggingReq = JSON.parseObject(reqBodyInJson, MemberTaggingReq.class);
// 构建通用
JSONObject jsonObject = buildCommon(memberTaggingReq);
// 会员基础业务数据填充
jsonObject.put("platAccount", memberTaggingReq.getUnionId());
JSONArray tagsMark = new JSONArray();
List<TaggingDto> tags = memberTaggingReq.getTags();
for (TaggingDto taggingDto : tags) {
    JSONObject tagObj = new JSONObject();
    tagObj.put("tagId", taggingDto.getTagId());
    JSONArray tagValueArr = new JSONArray();
    List<String> tagValues = taggingDto.getTagValues();
    for (String tagValue : tagValues) {
        tagValueArr.add(tagValue);
    }
    tagObj.put("tagValue", tagValueArr);
    tagsMark.add(tagObj);
}
jsonObject.put("tagsMark", tagsMark);
String result = jsonObject.toJSONString();
return result;

public JSONObject buildCommon(BaseMember baseMember) {
    // copy通用变量
    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
    return jsonObject;
}
public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
    jsonObject.put("name", memberRegReq.getMemberName());
    jsonObject.put("guideId", memberRegReq.getGuideCode());
    // 平台客户账号
    jsonObject.put("id", memberRegReq.getUnionId());
}


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.MemberTaggingReq;
import com.shuyun.cem.std.member.protocol.memtag.querymemtag.MemTagQueryReq;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import java.util.Objects;

// 反序列化为协议
MemTagQueryReq memTagQueryReq = JSON.parseObject(reqBodyInJson, MemTagQueryReq.class);
// 构建通用
JSONObject jsonObject = buildCommon(memTagQueryReq);
// 会员基础业务数据填充
jsonObject.put("platAccount", memTagQueryReq.getUnionId());
String result = jsonObject.toJSONString();
return result;

public JSONObject buildCommon(BaseMember baseMember) {
    // copy通用变量
    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
    return jsonObject;
}
public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
    jsonObject.put("name", memberRegReq.getMemberName());
    jsonObject.put("guideId", memberRegReq.getGuideCode());
    // 平台客户账号
    jsonObject.put("id", memberRegReq.getUnionId());
}


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import java.util.Objects;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memtag.memtagging.MemberTaggingRsp;
import com.shuyun.cem.std.member.protocol.memtag.querymemtag.MemTagDto;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

JSONObject rspObj = JSON.parseObject(rspBodyInJson);
Boolean success = rspObj.getBoolean("success");
if (!success) {
    RestWrap errRestWrap = new RestWrap();
    errRestWrap.setSuccess(false);
    errRestWrap.setCode(rspObj.getString("code"));
    errRestWrap.setMessage(rspObj.getString("message"));
    return JSON.toJSONString(errRestWrap);
}
return doRun(rspObj);


public String doRun(JSONObject rspObj) {
    List<MemTagDto> memTagDtos = new ArrayList<>();
    JSONArray jsonArray = rspObj.getJSONArray("data");
    if (Objects.nonNull(jsonArray)) {
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject itemObj = jsonArray.getJSONObject(i);
            MemTagDto memTagDto = new MemTagDto();
            memTagDto.setTagId(itemObj.getString("tagId"));
            memTagDto.setName(itemObj.getString("tagName"));
            JSONArray tagValueArr = itemObj.getJSONArray("tagValue");
            if (null != tagValueArr && !tagValueArr.isEmpty()) {
                memTagDto.setTagValues(tagValueArr.toJavaList(String.class));
            }
            memTagDtos.add(memTagDto);
        }
    }
    RestWrap<List<MemTagDto>> restWrap = new RestWrap<>();
    restWrap.buildSuccess(memTagDtos);
    String result = JSON.toJSONString(restWrap);
    return result;
}

public String groovyFileName() {
    return projectDir() + "." + "memTag.rspBody";
}

public String bizType() {
    return ConstantKey.ScriptBizType.rspBody;
}

public String apiCode() {
    return null;
}

public Map<String, Object> scriptInputParam() {
    JSONObject object = new JSONObject();
    object.put("success", true);
    object.put("code", "testCode");
    object.put("message", "testErrMsg");
    JSONArray jsonArray = new JSONArray();
    JSONObject dataObj = new JSONObject();
    dataObj.put("tagId", "134");
    jsonArray.add(dataObj);
    object.put("data", jsonArray);
    Map<String, Object> param = new HashMap<>();
    param.put(ConstantKey.RSP_BODY_EXTEND, object.toJSONString());
    return param;
}
public static void main(String[] args) throws IllegalAccessException, InstantiationException, ClassNotFoundException {
    AbsScriptGenerate absScriptGenerate = buildInstance();
    // 生成脚本
    ScriptContext scriptContext = absScriptGenerate.genRspBodyScript(true);
    // 测试脚本
    absScriptGenerate.testScript(scriptContext);
}


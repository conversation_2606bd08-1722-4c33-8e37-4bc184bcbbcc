import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import org.apache.commons.lang3.StringUtils;
import java.util.Objects;
import java.util.UUID;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaDto;
import org.apache.commons.lang3.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

JSONObject rspObj = JSON.parseObject(rspBodyInJson);
Boolean success = rspObj.getBoolean("success");
if (!success) {
    RestWrap errRestWrap = new RestWrap();
    errRestWrap.setSuccess(false);
    errRestWrap.setCode(rspObj.getString("code"));
    errRestWrap.setMessage(rspObj.getString("msg"));
    return JSON.toJSONString(errRestWrap);
}
return doRun(rspObj);
/**
 * 设置ASICS会员系统ID
 * 优先级：appCfgObj.membershipSystemId > inputParam.membershipSystemId > 默认值"1"
 *
 * @param asicsObject 输出对象
 * @param appCfgObj 应用配置对象
 * @param inputParam 输入参数对象
 */
public void setMembershipSystemId(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
    // 默认值
    String membershipSystemId = "1";
    // 优先从appCfgObj获取
    if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("membershipSystemId"))) {
        membershipSystemId = appCfgObj.getString("membershipSystemId");
    } else // 其次从inputParam获取
    if (inputParam != null && inputParam.containsKey("membershipSystemId") && inputParam.get("membershipSystemId") != null) {
        membershipSystemId = inputParam.getString("membershipSystemId");
    }
    asicsObject.put("membershipSystemId", membershipSystemId);
}
/**
 * 设置ASICS渠道信息
 * 优先级：appCfgObj.channel > inputParam.channel > 默认值"15"
 *
 * @param asicsObject 输出对象
 * @param appCfgObj 应用配置对象
 * @param inputParam 输入参数对象
 */
public void setChannel(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
    // 默认值
    String channel = "15";
    // 优先从appCfgObj获取
    if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("channel"))) {
        channel = appCfgObj.getString("channel");
    } else // 其次从inputParam获取
    if (inputParam != null && inputParam.containsKey("channel") && inputParam.get("channel") != null) {
        channel = inputParam.getString("channel");
    }
    asicsObject.put("channel", channel);
}


public String doRun(JSONObject rspObj) {
    RestWrap<List<GradeMetaDto>> restWrap = new RestWrap<>();
    // 处理ASICS等级配置响应数据
    List<GradeMetaDto> gradeMetaDtos = new ArrayList<>();
    JSONArray dataArray = rspObj.getJSONArray("data");
    if (Objects.nonNull(dataArray) && dataArray.size() > 0) {
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject gradeObj = dataArray.getJSONObject(i);
            GradeMetaDto gradeMetaDto = new GradeMetaDto();
            // 映射ASICS字段到标准GradeMetaDto字段
            if (gradeObj.containsKey("gradeId")) {
                gradeMetaDto.setGradeId(gradeObj.getString("gradeId"));
            }
            if (gradeObj.containsKey("gradeName")) {
                gradeMetaDto.setGradeName(gradeObj.getString("gradeName"));
            }
            if (gradeObj.containsKey("sortId")) {
                gradeMetaDto.setGradeSort(gradeObj.getInteger("sortId"));
            }
            // 保存原始数据作为扩展信息
            gradeMetaDto.setProjectExt(JSON.toJSONString(gradeObj));
            gradeMetaDtos.add(gradeMetaDto);
        }
    }
    restWrap.buildSuccess(gradeMetaDtos);
    String result = JSON.toJSONString(restWrap);
    return result;
}


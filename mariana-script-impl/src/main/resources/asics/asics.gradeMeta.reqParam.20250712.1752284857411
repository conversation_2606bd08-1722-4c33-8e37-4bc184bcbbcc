import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.base.gw.RequestHandle;
import com.shuyun.cem.std.member.protocol.grademeta.GradeMetaQueryReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import java.util.HashMap;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shuyun.cem.std.member.protocol.BaseMember;
import com.shuyun.cem.std.member.protocol.RestWrap;
import com.shuyun.cem.std.member.protocol.memreg.MemberRegReq;
import org.apache.commons.lang3.StringUtils;
import java.util.Objects;
import java.util.UUID;

// 反序列化请求body
GradeMetaQueryReq gradeMetaQueryReq = JSON.parseObject(reqBodyInJson, GradeMetaQueryReq.class);
JSONObject inputParam = JSON.parseObject(reqBodyInJson);
RequestHandle requestHandle = new RequestHandle();
RequestHandle.ShenyuRequestMethod requestMethod = new RequestHandle.ShenyuRequestMethod();
// 等级配置查询使用 GET 方法
requestMethod.setHttpMethod(HttpMethod.GET.name());
requestHandle.setRequestMethod(requestMethod);
// 从配置中获取其他头信息
String appCfgJson = gradeMetaQueryReq.getAppCfgJson();
// 增加查询参数
RequestHandle.ShenyuRequestParameter requestParameter = new RequestHandle.ShenyuRequestParameter();
Map<String, String> addParameters = new HashMap<>();
// membershipSystemId 是必须参数
if (StringUtils.isNotEmpty(appCfgJson)) {
    JSONObject appCfgObj = JSON.parseObject(appCfgJson);
    String membershipSystemId = appCfgObj.getString("membershipSystemId");
    if (StringUtils.isNotEmpty(membershipSystemId)) {
        addParameters.put("membershipSystemId", membershipSystemId);
    } else {
        // 默认值
        addParameters.put("membershipSystemId", "1");
    }
} else {
    // 默认值
    addParameters.put("membershipSystemId", "1");
}
requestParameter.setAddParameters(addParameters);
requestHandle.setParameter(requestParameter);
String result = JSON.toJSONString(requestHandle);
return result;

public JSONObject buildCommon(BaseMember baseMember) {
    // copy通用变量
    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseMember));
    return jsonObject;
}
public void buildMemberBiz(JSONObject jsonObject, MemberRegReq memberRegReq) {
    jsonObject.put("fullName", memberRegReq.getMemberName());
    jsonObject.put("nick", memberRegReq.getNickName());
    jsonObject.put("dateOfBirth", memberRegReq.getBirthday());
    // M:已婚 S:未婚 D:离异 O:其他
    String marrage = "O";
    // 0未婚1已婚2未知
    if (Objects.isNull(memberRegReq.getMarriageStatus())) {
        marrage = "O";
    } else if (memberRegReq.getMarriageStatus().equals(2)) {
        marrage = "O";
    } else if (memberRegReq.getMarriageStatus().equals(0)) {
        marrage = "S";
    } else if (memberRegReq.getMarriageStatus().equals(1)) {
        marrage = "M";
    }
    jsonObject.put("marriage", marrage);
}
/**
 * 设置ASICS会员系统ID
 * 优先级：appCfgObj.membershipSystemId > inputParam.membershipSystemId > 默认值"1"
 *
 * @param asicsObject 输出对象
 * @param appCfgObj 应用配置对象
 * @param inputParam 输入参数对象
 */
public void setMembershipSystemId(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
    // 默认值
    String membershipSystemId = "1";
    // 优先从appCfgObj获取
    if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("membershipSystemId"))) {
        membershipSystemId = appCfgObj.getString("membershipSystemId");
    } else // 其次从inputParam获取
    if (inputParam != null && inputParam.containsKey("membershipSystemId") && inputParam.get("membershipSystemId") != null) {
        membershipSystemId = inputParam.getString("membershipSystemId");
    }
    asicsObject.put("membershipSystemId", membershipSystemId);
}
/**
 * 设置ASICS渠道信息
 * 优先级：appCfgObj.channel > inputParam.channel > 默认值"15"
 *
 * @param asicsObject 输出对象
 * @param appCfgObj 应用配置对象
 * @param inputParam 输入参数对象
 */
public void setChannel(JSONObject asicsObject, JSONObject appCfgObj, JSONObject inputParam) {
    // 默认值
    String channel = "15";
    // 优先从appCfgObj获取
    if (appCfgObj != null && StringUtils.isNotEmpty(appCfgObj.getString("channel"))) {
        channel = appCfgObj.getString("channel");
    } else // 其次从inputParam获取
    if (inputParam != null && inputParam.containsKey("channel") && inputParam.get("channel") != null) {
        channel = inputParam.getString("channel");
    }
    asicsObject.put("channel", channel);
}


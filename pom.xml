<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shuyun</groupId>
    <artifactId>mariana-script-family</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <module>mariana-script-common</module>
        <module>mariana-script-impl</module>
    </modules>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <revision>1.0.1</revision>

        <shuyun.common.version>2023.*********.3-SNAPSHOT</shuyun.common.version>
        <shuyun.tianji.version>1.0.3</shuyun.tianji.version>
        <spectrum.version>3.4.1.RELEASE</spectrum.version>

        <fastjson.version>1.2.73</fastjson.version>
        <guava.version>27.0-jre</guava.version>
        <hibernate-validator.version>6.0.13.Final</hibernate-validator.version>
        <jjwt.version>0.9.1</jjwt.version>
        <shuyun-platform-starter.version>*******</shuyun-platform-starter.version>
        <hutool.version>5.7.16</hutool.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.24</version>
        </dependency>
    </dependencies>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>com.shuyun</groupId>
                <artifactId>mariana-script-common</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun</groupId>
                <artifactId>mariana-script-impl</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>


</project>
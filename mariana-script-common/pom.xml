<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mariana-script-family</artifactId>
        <groupId>com.shuyun</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mariana-script-common</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>gateway-script-util</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- todo 增加到网关依赖中 -->
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <version>2.4.15</version>
        </dependency>
    </dependencies>

</project>
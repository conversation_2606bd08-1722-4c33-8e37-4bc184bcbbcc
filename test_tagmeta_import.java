// 测试 TagMetaQueryReq 导入是否正确
import com.shuyun.cem.std.member.protocol.memtag.tagmeta.kylin.TagMetaQueryReq;

public class TestTagMetaImport {
    public static void main(String[] args) {
        // 测试能否正常创建 TagMetaQueryReq 对象
        TagMetaQueryReq req = new TagMetaQueryReq();
        System.out.println("TagMetaQueryReq 导入成功！");
        System.out.println("类名: " + req.getClass().getSimpleName());
        System.out.println("包路径: " + req.getClass().getPackage().getName());
    }
}

{"企微类目标签查询改造": {"功能说明": "获取企微类目的标签配置", "请求方式": "GET", "参数说明": {"membershipSystemId": {"类型": "integer(int64)", "位置": "query", "是否必须": false, "说明": "会员系统ID"}}}, "改造内容": {"1. 导入修正": "修正了导入语句，移除了错误的导入", "2. 参数处理": "支持membershipSystemId可选参数", "3. 企微类目": "添加categoryType=QIWEI参数", "4. 分页支持": "支持page和pageSize参数", "5. 编码修复": "修复了中文注释的编码问题"}, "测试用例": {"输入参数": {"membershipSystemId": "1", "categoryType": "QIWEI", "page": "1", "pageSize": "100", "appCfgJson": "{\"membershipSystemId\":\"1\",\"programCode\":\"ASICS001\"}"}, "预期输出": {"httpMethod": "GET", "queryParams": {"membershipSystemId": "1", "categoryType": "QIWEI", "pageNo": "1", "pageSize": "100", "programCode": "ASICS001"}}}, "使用场景": ["获取企微客户标签配置", "企微客户分类管理", "标签元数据查询", "客户画像标签获取"]}
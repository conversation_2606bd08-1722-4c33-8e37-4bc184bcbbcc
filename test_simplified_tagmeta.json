{"简化后的企微标签查询脚本": {"参考实现": "whoo目录下tagmeta的MdyReqParamScript", "简化内容": ["移除了复杂的TagMetaQueryReq导入", "移除了appCfgJson解析逻辑", "移除了多余的参数处理", "只保留核心的membershipSystemId参数"]}, "请求参数": {"membershipSystemId": {"类型": "integer(int64)", "位置": "query", "是否必须": false, "说明": "会员系统ID"}}, "核心逻辑": {"HTTP方法": "GET", "参数处理": "直接从inputBody获取membershipSystemId", "可选性": "如果没有提供membershipSystemId，则不添加该参数"}, "测试用例": {"有membershipSystemId": {"输入": {"membershipSystemId": "1", "appId": "wxf492c06764b16035"}, "预期输出": {"httpMethod": "GET", "queryParams": {"membershipSystemId": "1"}}}, "无membershipSystemId": {"输入": {"appId": "wxf492c06764b16035"}, "预期输出": {"httpMethod": "GET", "queryParams": {}}}}, "与whoo的对比": {"相同点": ["都使用GET方法", "都直接从inputBody获取参数", "都使用简洁的参数处理逻辑"], "不同点": ["whoo处理categoryIds和分页参数", "asics只处理membershipSystemId参数", "参数名称和业务逻辑不同"]}}